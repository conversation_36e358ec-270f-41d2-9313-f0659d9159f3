{"ast": null, "code": "/**\n * 错误处理工具类\n * 提供统一的错误处理、日志记录和用户友好的错误提示\n */\n\nimport { showError, showWarning } from './notification.js';\n\n/**\n * 错误类型枚举\n */\nexport const ErrorTypes = {\n  NETWORK: 'network',\n  VALIDATION: 'validation',\n  AUTHENTICATION: 'authentication',\n  AUTHORIZATION: 'authorization',\n  SERVER: 'server',\n  CLIENT: 'client',\n  UNKNOWN: 'unknown'\n};\n\n/**\n * 错误级别枚举\n */\nexport const ErrorLevels = {\n  LOW: 'low',\n  MEDIUM: 'medium',\n  HIGH: 'high',\n  CRITICAL: 'critical'\n};\n\n/**\n * 错误处理器类\n */\nexport class ErrorHandler {\n  constructor() {\n    this.errorLog = [];\n    this.maxLogSize = 100;\n    this.setupGlobalErrorHandlers();\n  }\n\n  /**\n   * 设置全局错误处理器\n   */\n  setupGlobalErrorHandlers() {\n    // 捕获未处理的Promise拒绝\n    window.addEventListener('unhandledrejection', event => {\n      this.handleError(event.reason, ErrorTypes.UNKNOWN, ErrorLevels.HIGH);\n      event.preventDefault();\n    });\n\n    // 捕获全局JavaScript错误\n    window.addEventListener('error', event => {\n      this.handleError(event.error, ErrorTypes.CLIENT, ErrorLevels.MEDIUM);\n    });\n  }\n\n  /**\n   * 处理错误\n   * @param {Error|string} error 错误对象或错误消息\n   * @param {string} type 错误类型\n   * @param {string} level 错误级别\n   * @param {Object} context 错误上下文\n   */\n  handleError(error, type = ErrorTypes.UNKNOWN, level = ErrorLevels.MEDIUM, context = {}) {\n    const errorInfo = this.normalizeError(error, type, level, context);\n\n    // 记录错误日志\n    this.logError(errorInfo);\n\n    // 显示用户友好的错误提示\n    this.showUserError(errorInfo);\n\n    // 根据错误级别执行相应操作\n    this.handleErrorByLevel(errorInfo);\n    return errorInfo;\n  }\n\n  /**\n   * 标准化错误对象\n   * @param {Error|string} error 错误\n   * @param {string} type 错误类型\n   * @param {string} level 错误级别\n   * @param {Object} context 上下文\n   * @returns {Object} 标准化的错误对象\n   */\n  normalizeError(error, type, level, context) {\n    const timestamp = new Date();\n    const id = `error_${timestamp.getTime()}_${Math.random().toString(36).substr(2, 9)}`;\n    let message = '未知错误';\n    let stack = null;\n    if (error instanceof Error) {\n      message = error.message;\n      stack = error.stack;\n    } else if (typeof error === 'string') {\n      message = error;\n    } else if (error && error.message) {\n      message = error.message;\n    }\n    return {\n      id,\n      message,\n      stack,\n      type,\n      level,\n      timestamp,\n      context,\n      userAgent: navigator.userAgent,\n      url: window.location.href\n    };\n  }\n\n  /**\n   * 记录错误日志\n   * @param {Object} errorInfo 错误信息\n   */\n  logError(errorInfo) {\n    // 添加到内存日志\n    this.errorLog.unshift(errorInfo);\n\n    // 限制日志大小\n    if (this.errorLog.length > this.maxLogSize) {\n      this.errorLog = this.errorLog.slice(0, this.maxLogSize);\n    }\n\n    // 控制台输出\n    console.error(`[${errorInfo.level.toUpperCase()}] ${errorInfo.type}: ${errorInfo.message}`, errorInfo);\n\n    // 发送到服务器（在实际应用中）\n    this.sendErrorToServer(errorInfo);\n  }\n\n  /**\n   * 发送错误到服务器\n   * @param {Object} errorInfo 错误信息\n   */\n  sendErrorToServer(errorInfo) {\n    // 在实际应用中，这里会发送错误到服务器\n    // 这里只是模拟\n    if (errorInfo.level === ErrorLevels.CRITICAL) {\n      console.warn('Critical error would be sent to server:', errorInfo);\n    }\n  }\n\n  /**\n   * 显示用户友好的错误提示\n   * @param {Object} errorInfo 错误信息\n   */\n  showUserError(errorInfo) {\n    const userMessage = this.getUserFriendlyMessage(errorInfo);\n    if (errorInfo.level === ErrorLevels.CRITICAL || errorInfo.level === ErrorLevels.HIGH) {\n      showError(userMessage, '错误', {\n        autoClose: false\n      });\n    } else if (errorInfo.level === ErrorLevels.MEDIUM) {\n      showWarning(userMessage, '警告');\n    }\n    // LOW级别的错误不显示给用户\n  }\n\n  /**\n   * 获取用户友好的错误消息\n   * @param {Object} errorInfo 错误信息\n   * @returns {string} 用户友好的消息\n   */\n  getUserFriendlyMessage(errorInfo) {\n    const messageMap = {\n      [ErrorTypes.NETWORK]: '网络连接异常，请检查网络设置',\n      [ErrorTypes.VALIDATION]: '输入数据不符合要求，请检查后重试',\n      [ErrorTypes.AUTHENTICATION]: '身份验证失败，请重新登录',\n      [ErrorTypes.AUTHORIZATION]: '权限不足，无法执行此操作',\n      [ErrorTypes.SERVER]: '服务器暂时无法响应，请稍后重试',\n      [ErrorTypes.CLIENT]: '客户端错误，请刷新页面重试'\n    };\n    return messageMap[errorInfo.type] || '操作失败，请稍后重试';\n  }\n\n  /**\n   * 根据错误级别处理\n   * @param {Object} errorInfo 错误信息\n   */\n  handleErrorByLevel(errorInfo) {\n    switch (errorInfo.level) {\n      case ErrorLevels.CRITICAL:\n        // 关键错误：可能需要重新加载页面或跳转到错误页面\n        console.error('Critical error detected:', errorInfo);\n        break;\n      case ErrorLevels.HIGH:\n        // 高级错误：记录详细信息，可能影响用户体验\n        console.warn('High level error:', errorInfo);\n        break;\n      case ErrorLevels.MEDIUM:\n        // 中级错误：记录信息，用户可以继续使用\n        console.info('Medium level error:', errorInfo);\n        break;\n      case ErrorLevels.LOW:\n        // 低级错误：仅记录，不影响用户体验\n        console.debug('Low level error:', errorInfo);\n        break;\n    }\n  }\n\n  /**\n   * 获取错误日志\n   * @param {number} limit 限制数量\n   * @returns {Array} 错误日志列表\n   */\n  getErrorLog(limit = 50) {\n    return this.errorLog.slice(0, limit);\n  }\n\n  /**\n   * 清空错误日志\n   */\n  clearErrorLog() {\n    this.errorLog = [];\n  }\n\n  /**\n   * 获取错误统计\n   * @returns {Object} 错误统计信息\n   */\n  getErrorStats() {\n    const stats = {\n      total: this.errorLog.length,\n      byType: {},\n      byLevel: {},\n      recent: 0 // 最近1小时的错误数\n    };\n    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\n    this.errorLog.forEach(error => {\n      // 按类型统计\n      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;\n\n      // 按级别统计\n      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1;\n\n      // 最近错误统计\n      if (error.timestamp > oneHourAgo) {\n        stats.recent++;\n      }\n    });\n    return stats;\n  }\n}\n\n// 创建全局错误处理器实例\nexport const globalErrorHandler = new ErrorHandler();\n\n/**\n * 便捷的错误处理函数\n */\nexport function handleError(error, type, level, context) {\n  return globalErrorHandler.handleError(error, type, level, context);\n}\n\n/**\n * 网络错误处理\n */\nexport function handleNetworkError(error, context = {}) {\n  return handleError(error, ErrorTypes.NETWORK, ErrorLevels.HIGH, context);\n}\n\n/**\n * 验证错误处理\n */\nexport function handleValidationError(error, context = {}) {\n  return handleError(error, ErrorTypes.VALIDATION, ErrorLevels.MEDIUM, context);\n}\n\n/**\n * 服务器错误处理\n */\nexport function handleServerError(error, context = {}) {\n  return handleError(error, ErrorTypes.SERVER, ErrorLevels.HIGH, context);\n}\n\n/**\n * 异步操作错误处理装饰器\n */\nexport function withErrorHandling(asyncFn, errorType = ErrorTypes.UNKNOWN, errorLevel = ErrorLevels.MEDIUM) {\n  return async function (...args) {\n    try {\n      return await asyncFn.apply(this, args);\n    } catch (error) {\n      handleError(error, errorType, errorLevel, {\n        function: asyncFn.name,\n        arguments: args\n      });\n      throw error;\n    }\n  };\n}\n\n/**\n * Promise错误处理\n */\nexport function handlePromise(promise, errorType = ErrorTypes.UNKNOWN, errorLevel = ErrorLevels.MEDIUM) {\n  return promise.catch(error => {\n    handleError(error, errorType, errorLevel);\n    throw error;\n  });\n}\nexport default globalErrorHandler;", "map": {"version": 3, "names": ["showError", "showWarning", "ErrorTypes", "NETWORK", "VALIDATION", "AUTHENTICATION", "AUTHORIZATION", "SERVER", "CLIENT", "UNKNOWN", "ErrorLevels", "LOW", "MEDIUM", "HIGH", "CRITICAL", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "errorLog", "maxLogSize", "setupGlobalErrorHandlers", "window", "addEventListener", "event", "handleError", "reason", "preventDefault", "error", "type", "level", "context", "errorInfo", "normalizeError", "logError", "showUserError", "handleErrorByLevel", "timestamp", "Date", "id", "getTime", "Math", "random", "toString", "substr", "message", "stack", "Error", "userAgent", "navigator", "url", "location", "href", "unshift", "length", "slice", "console", "toUpperCase", "sendErrorToServer", "warn", "userMessage", "getUserFriendlyMessage", "autoClose", "messageMap", "info", "debug", "getErrorLog", "limit", "clearErrorLog", "getErrorStats", "stats", "total", "byType", "byLevel", "recent", "oneHourAgo", "now", "for<PERSON>ach", "globalErrorHandler", "handleNetworkError", "handleValidationError", "handleServerError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asyncFn", "errorType", "errorLevel", "args", "apply", "function", "name", "arguments", "handlePromise", "promise", "catch"], "sources": ["D:/demo/ooo/pass/src/utils/errorHandler.js"], "sourcesContent": ["/**\n * 错误处理工具类\n * 提供统一的错误处理、日志记录和用户友好的错误提示\n */\n\nimport { showError, showWarning } from './notification.js'\n\n/**\n * 错误类型枚举\n */\nexport const ErrorTypes = {\n  NETWORK: 'network',\n  VALIDATION: 'validation',\n  AUTHENTICATION: 'authentication',\n  AUTHORIZATION: 'authorization',\n  SERVER: 'server',\n  CLIENT: 'client',\n  UNKNOWN: 'unknown'\n}\n\n/**\n * 错误级别枚举\n */\nexport const ErrorLevels = {\n  LOW: 'low',\n  MEDIUM: 'medium',\n  HIGH: 'high',\n  CRITICAL: 'critical'\n}\n\n/**\n * 错误处理器类\n */\nexport class ErrorHandler {\n  constructor() {\n    this.errorLog = []\n    this.maxLogSize = 100\n    this.setupGlobalErrorHandlers()\n  }\n\n  /**\n   * 设置全局错误处理器\n   */\n  setupGlobalErrorHandlers() {\n    // 捕获未处理的Promise拒绝\n    window.addEventListener('unhandledrejection', (event) => {\n      this.handleError(event.reason, ErrorTypes.UNKNOWN, ErrorLevels.HIGH)\n      event.preventDefault()\n    })\n\n    // 捕获全局JavaScript错误\n    window.addEventListener('error', (event) => {\n      this.handleError(event.error, ErrorTypes.CLIENT, ErrorLevels.MEDIUM)\n    })\n  }\n\n  /**\n   * 处理错误\n   * @param {Error|string} error 错误对象或错误消息\n   * @param {string} type 错误类型\n   * @param {string} level 错误级别\n   * @param {Object} context 错误上下文\n   */\n  handleError(error, type = ErrorTypes.UNKNOWN, level = ErrorLevels.MEDIUM, context = {}) {\n    const errorInfo = this.normalizeError(error, type, level, context)\n    \n    // 记录错误日志\n    this.logError(errorInfo)\n    \n    // 显示用户友好的错误提示\n    this.showUserError(errorInfo)\n    \n    // 根据错误级别执行相应操作\n    this.handleErrorByLevel(errorInfo)\n    \n    return errorInfo\n  }\n\n  /**\n   * 标准化错误对象\n   * @param {Error|string} error 错误\n   * @param {string} type 错误类型\n   * @param {string} level 错误级别\n   * @param {Object} context 上下文\n   * @returns {Object} 标准化的错误对象\n   */\n  normalizeError(error, type, level, context) {\n    const timestamp = new Date()\n    const id = `error_${timestamp.getTime()}_${Math.random().toString(36).substr(2, 9)}`\n    \n    let message = '未知错误'\n    let stack = null\n    \n    if (error instanceof Error) {\n      message = error.message\n      stack = error.stack\n    } else if (typeof error === 'string') {\n      message = error\n    } else if (error && error.message) {\n      message = error.message\n    }\n    \n    return {\n      id,\n      message,\n      stack,\n      type,\n      level,\n      timestamp,\n      context,\n      userAgent: navigator.userAgent,\n      url: window.location.href\n    }\n  }\n\n  /**\n   * 记录错误日志\n   * @param {Object} errorInfo 错误信息\n   */\n  logError(errorInfo) {\n    // 添加到内存日志\n    this.errorLog.unshift(errorInfo)\n    \n    // 限制日志大小\n    if (this.errorLog.length > this.maxLogSize) {\n      this.errorLog = this.errorLog.slice(0, this.maxLogSize)\n    }\n    \n    // 控制台输出\n    console.error(`[${errorInfo.level.toUpperCase()}] ${errorInfo.type}: ${errorInfo.message}`, errorInfo)\n    \n    // 发送到服务器（在实际应用中）\n    this.sendErrorToServer(errorInfo)\n  }\n\n  /**\n   * 发送错误到服务器\n   * @param {Object} errorInfo 错误信息\n   */\n  sendErrorToServer(errorInfo) {\n    // 在实际应用中，这里会发送错误到服务器\n    // 这里只是模拟\n    if (errorInfo.level === ErrorLevels.CRITICAL) {\n      console.warn('Critical error would be sent to server:', errorInfo)\n    }\n  }\n\n  /**\n   * 显示用户友好的错误提示\n   * @param {Object} errorInfo 错误信息\n   */\n  showUserError(errorInfo) {\n    const userMessage = this.getUserFriendlyMessage(errorInfo)\n    \n    if (errorInfo.level === ErrorLevels.CRITICAL || errorInfo.level === ErrorLevels.HIGH) {\n      showError(userMessage, '错误', { autoClose: false })\n    } else if (errorInfo.level === ErrorLevels.MEDIUM) {\n      showWarning(userMessage, '警告')\n    }\n    // LOW级别的错误不显示给用户\n  }\n\n  /**\n   * 获取用户友好的错误消息\n   * @param {Object} errorInfo 错误信息\n   * @returns {string} 用户友好的消息\n   */\n  getUserFriendlyMessage(errorInfo) {\n    const messageMap = {\n      [ErrorTypes.NETWORK]: '网络连接异常，请检查网络设置',\n      [ErrorTypes.VALIDATION]: '输入数据不符合要求，请检查后重试',\n      [ErrorTypes.AUTHENTICATION]: '身份验证失败，请重新登录',\n      [ErrorTypes.AUTHORIZATION]: '权限不足，无法执行此操作',\n      [ErrorTypes.SERVER]: '服务器暂时无法响应，请稍后重试',\n      [ErrorTypes.CLIENT]: '客户端错误，请刷新页面重试'\n    }\n    \n    return messageMap[errorInfo.type] || '操作失败，请稍后重试'\n  }\n\n  /**\n   * 根据错误级别处理\n   * @param {Object} errorInfo 错误信息\n   */\n  handleErrorByLevel(errorInfo) {\n    switch (errorInfo.level) {\n      case ErrorLevels.CRITICAL:\n        // 关键错误：可能需要重新加载页面或跳转到错误页面\n        console.error('Critical error detected:', errorInfo)\n        break\n      case ErrorLevels.HIGH:\n        // 高级错误：记录详细信息，可能影响用户体验\n        console.warn('High level error:', errorInfo)\n        break\n      case ErrorLevels.MEDIUM:\n        // 中级错误：记录信息，用户可以继续使用\n        console.info('Medium level error:', errorInfo)\n        break\n      case ErrorLevels.LOW:\n        // 低级错误：仅记录，不影响用户体验\n        console.debug('Low level error:', errorInfo)\n        break\n    }\n  }\n\n  /**\n   * 获取错误日志\n   * @param {number} limit 限制数量\n   * @returns {Array} 错误日志列表\n   */\n  getErrorLog(limit = 50) {\n    return this.errorLog.slice(0, limit)\n  }\n\n  /**\n   * 清空错误日志\n   */\n  clearErrorLog() {\n    this.errorLog = []\n  }\n\n  /**\n   * 获取错误统计\n   * @returns {Object} 错误统计信息\n   */\n  getErrorStats() {\n    const stats = {\n      total: this.errorLog.length,\n      byType: {},\n      byLevel: {},\n      recent: 0 // 最近1小时的错误数\n    }\n    \n    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)\n    \n    this.errorLog.forEach(error => {\n      // 按类型统计\n      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1\n      \n      // 按级别统计\n      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1\n      \n      // 最近错误统计\n      if (error.timestamp > oneHourAgo) {\n        stats.recent++\n      }\n    })\n    \n    return stats\n  }\n}\n\n// 创建全局错误处理器实例\nexport const globalErrorHandler = new ErrorHandler()\n\n/**\n * 便捷的错误处理函数\n */\nexport function handleError(error, type, level, context) {\n  return globalErrorHandler.handleError(error, type, level, context)\n}\n\n/**\n * 网络错误处理\n */\nexport function handleNetworkError(error, context = {}) {\n  return handleError(error, ErrorTypes.NETWORK, ErrorLevels.HIGH, context)\n}\n\n/**\n * 验证错误处理\n */\nexport function handleValidationError(error, context = {}) {\n  return handleError(error, ErrorTypes.VALIDATION, ErrorLevels.MEDIUM, context)\n}\n\n/**\n * 服务器错误处理\n */\nexport function handleServerError(error, context = {}) {\n  return handleError(error, ErrorTypes.SERVER, ErrorLevels.HIGH, context)\n}\n\n/**\n * 异步操作错误处理装饰器\n */\nexport function withErrorHandling(asyncFn, errorType = ErrorTypes.UNKNOWN, errorLevel = ErrorLevels.MEDIUM) {\n  return async function(...args) {\n    try {\n      return await asyncFn.apply(this, args)\n    } catch (error) {\n      handleError(error, errorType, errorLevel, { \n        function: asyncFn.name,\n        arguments: args \n      })\n      throw error\n    }\n  }\n}\n\n/**\n * Promise错误处理\n */\nexport function handlePromise(promise, errorType = ErrorTypes.UNKNOWN, errorLevel = ErrorLevels.MEDIUM) {\n  return promise.catch(error => {\n    handleError(error, errorType, errorLevel)\n    throw error\n  })\n}\n\nexport default globalErrorHandler\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,SAAS,EAAEC,WAAW,QAAQ,mBAAmB;;AAE1D;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,YAAY;EACxBC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,YAAY,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;EACEA,wBAAwBA,CAAA,EAAG;IACzB;IACAC,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;MACvD,IAAI,CAACC,WAAW,CAACD,KAAK,CAACE,MAAM,EAAEtB,UAAU,CAACO,OAAO,EAAEC,WAAW,CAACG,IAAI,CAAC;MACpES,KAAK,CAACG,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MAC1C,IAAI,CAACC,WAAW,CAACD,KAAK,CAACI,KAAK,EAAExB,UAAU,CAACM,MAAM,EAAEE,WAAW,CAACE,MAAM,CAAC;IACtE,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEW,WAAWA,CAACG,KAAK,EAAEC,IAAI,GAAGzB,UAAU,CAACO,OAAO,EAAEmB,KAAK,GAAGlB,WAAW,CAACE,MAAM,EAAEiB,OAAO,GAAG,CAAC,CAAC,EAAE;IACtF,MAAMC,SAAS,GAAG,IAAI,CAACC,cAAc,CAACL,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,CAAC;;IAElE;IACA,IAAI,CAACG,QAAQ,CAACF,SAAS,CAAC;;IAExB;IACA,IAAI,CAACG,aAAa,CAACH,SAAS,CAAC;;IAE7B;IACA,IAAI,CAACI,kBAAkB,CAACJ,SAAS,CAAC;IAElC,OAAOA,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,cAAcA,CAACL,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC1C,MAAMM,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC5B,MAAMC,EAAE,GAAG,SAASF,SAAS,CAACG,OAAO,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEpF,IAAIC,OAAO,GAAG,MAAM;IACpB,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAIlB,KAAK,YAAYmB,KAAK,EAAE;MAC1BF,OAAO,GAAGjB,KAAK,CAACiB,OAAO;MACvBC,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACrB,CAAC,MAAM,IAAI,OAAOlB,KAAK,KAAK,QAAQ,EAAE;MACpCiB,OAAO,GAAGjB,KAAK;IACjB,CAAC,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACiB,OAAO,EAAE;MACjCA,OAAO,GAAGjB,KAAK,CAACiB,OAAO;IACzB;IAEA,OAAO;MACLN,EAAE;MACFM,OAAO;MACPC,KAAK;MACLjB,IAAI;MACJC,KAAK;MACLO,SAAS;MACTN,OAAO;MACPiB,SAAS,EAAEC,SAAS,CAACD,SAAS;MAC9BE,GAAG,EAAE5B,MAAM,CAAC6B,QAAQ,CAACC;IACvB,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACElB,QAAQA,CAACF,SAAS,EAAE;IAClB;IACA,IAAI,CAACb,QAAQ,CAACkC,OAAO,CAACrB,SAAS,CAAC;;IAEhC;IACA,IAAI,IAAI,CAACb,QAAQ,CAACmC,MAAM,GAAG,IAAI,CAAClC,UAAU,EAAE;MAC1C,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACnC,UAAU,CAAC;IACzD;;IAEA;IACAoC,OAAO,CAAC5B,KAAK,CAAC,IAAII,SAAS,CAACF,KAAK,CAAC2B,WAAW,CAAC,CAAC,KAAKzB,SAAS,CAACH,IAAI,KAAKG,SAAS,CAACa,OAAO,EAAE,EAAEb,SAAS,CAAC;;IAEtG;IACA,IAAI,CAAC0B,iBAAiB,CAAC1B,SAAS,CAAC;EACnC;;EAEA;AACF;AACA;AACA;EACE0B,iBAAiBA,CAAC1B,SAAS,EAAE;IAC3B;IACA;IACA,IAAIA,SAAS,CAACF,KAAK,KAAKlB,WAAW,CAACI,QAAQ,EAAE;MAC5CwC,OAAO,CAACG,IAAI,CAAC,yCAAyC,EAAE3B,SAAS,CAAC;IACpE;EACF;;EAEA;AACF;AACA;AACA;EACEG,aAAaA,CAACH,SAAS,EAAE;IACvB,MAAM4B,WAAW,GAAG,IAAI,CAACC,sBAAsB,CAAC7B,SAAS,CAAC;IAE1D,IAAIA,SAAS,CAACF,KAAK,KAAKlB,WAAW,CAACI,QAAQ,IAAIgB,SAAS,CAACF,KAAK,KAAKlB,WAAW,CAACG,IAAI,EAAE;MACpFb,SAAS,CAAC0D,WAAW,EAAE,IAAI,EAAE;QAAEE,SAAS,EAAE;MAAM,CAAC,CAAC;IACpD,CAAC,MAAM,IAAI9B,SAAS,CAACF,KAAK,KAAKlB,WAAW,CAACE,MAAM,EAAE;MACjDX,WAAW,CAACyD,WAAW,EAAE,IAAI,CAAC;IAChC;IACA;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEC,sBAAsBA,CAAC7B,SAAS,EAAE;IAChC,MAAM+B,UAAU,GAAG;MACjB,CAAC3D,UAAU,CAACC,OAAO,GAAG,gBAAgB;MACtC,CAACD,UAAU,CAACE,UAAU,GAAG,kBAAkB;MAC3C,CAACF,UAAU,CAACG,cAAc,GAAG,cAAc;MAC3C,CAACH,UAAU,CAACI,aAAa,GAAG,cAAc;MAC1C,CAACJ,UAAU,CAACK,MAAM,GAAG,iBAAiB;MACtC,CAACL,UAAU,CAACM,MAAM,GAAG;IACvB,CAAC;IAED,OAAOqD,UAAU,CAAC/B,SAAS,CAACH,IAAI,CAAC,IAAI,YAAY;EACnD;;EAEA;AACF;AACA;AACA;EACEO,kBAAkBA,CAACJ,SAAS,EAAE;IAC5B,QAAQA,SAAS,CAACF,KAAK;MACrB,KAAKlB,WAAW,CAACI,QAAQ;QACvB;QACAwC,OAAO,CAAC5B,KAAK,CAAC,0BAA0B,EAAEI,SAAS,CAAC;QACpD;MACF,KAAKpB,WAAW,CAACG,IAAI;QACnB;QACAyC,OAAO,CAACG,IAAI,CAAC,mBAAmB,EAAE3B,SAAS,CAAC;QAC5C;MACF,KAAKpB,WAAW,CAACE,MAAM;QACrB;QACA0C,OAAO,CAACQ,IAAI,CAAC,qBAAqB,EAAEhC,SAAS,CAAC;QAC9C;MACF,KAAKpB,WAAW,CAACC,GAAG;QAClB;QACA2C,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEjC,SAAS,CAAC;QAC5C;IACJ;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEkC,WAAWA,CAACC,KAAK,GAAG,EAAE,EAAE;IACtB,OAAO,IAAI,CAAChD,QAAQ,CAACoC,KAAK,CAAC,CAAC,EAAEY,KAAK,CAAC;EACtC;;EAEA;AACF;AACA;EACEC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACjD,QAAQ,GAAG,EAAE;EACpB;;EAEA;AACF;AACA;AACA;EACEkD,aAAaA,CAAA,EAAG;IACd,MAAMC,KAAK,GAAG;MACZC,KAAK,EAAE,IAAI,CAACpD,QAAQ,CAACmC,MAAM;MAC3BkB,MAAM,EAAE,CAAC,CAAC;MACVC,OAAO,EAAE,CAAC,CAAC;MACXC,MAAM,EAAE,CAAC,CAAC;IACZ,CAAC;IAED,MAAMC,UAAU,GAAG,IAAIrC,IAAI,CAACA,IAAI,CAACsC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExD,IAAI,CAACzD,QAAQ,CAAC0D,OAAO,CAACjD,KAAK,IAAI;MAC7B;MACA0C,KAAK,CAACE,MAAM,CAAC5C,KAAK,CAACC,IAAI,CAAC,GAAG,CAACyC,KAAK,CAACE,MAAM,CAAC5C,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;MAE9D;MACAyC,KAAK,CAACG,OAAO,CAAC7C,KAAK,CAACE,KAAK,CAAC,GAAG,CAACwC,KAAK,CAACG,OAAO,CAAC7C,KAAK,CAACE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;MAElE;MACA,IAAIF,KAAK,CAACS,SAAS,GAAGsC,UAAU,EAAE;QAChCL,KAAK,CAACI,MAAM,EAAE;MAChB;IACF,CAAC,CAAC;IAEF,OAAOJ,KAAK;EACd;AACF;;AAEA;AACA,OAAO,MAAMQ,kBAAkB,GAAG,IAAI7D,YAAY,CAAC,CAAC;;AAEpD;AACA;AACA;AACA,OAAO,SAASQ,WAAWA,CAACG,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACvD,OAAO+C,kBAAkB,CAACrD,WAAW,CAACG,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,CAAC;AACpE;;AAEA;AACA;AACA;AACA,OAAO,SAASgD,kBAAkBA,CAACnD,KAAK,EAAEG,OAAO,GAAG,CAAC,CAAC,EAAE;EACtD,OAAON,WAAW,CAACG,KAAK,EAAExB,UAAU,CAACC,OAAO,EAAEO,WAAW,CAACG,IAAI,EAAEgB,OAAO,CAAC;AAC1E;;AAEA;AACA;AACA;AACA,OAAO,SAASiD,qBAAqBA,CAACpD,KAAK,EAAEG,OAAO,GAAG,CAAC,CAAC,EAAE;EACzD,OAAON,WAAW,CAACG,KAAK,EAAExB,UAAU,CAACE,UAAU,EAAEM,WAAW,CAACE,MAAM,EAAEiB,OAAO,CAAC;AAC/E;;AAEA;AACA;AACA;AACA,OAAO,SAASkD,iBAAiBA,CAACrD,KAAK,EAAEG,OAAO,GAAG,CAAC,CAAC,EAAE;EACrD,OAAON,WAAW,CAACG,KAAK,EAAExB,UAAU,CAACK,MAAM,EAAEG,WAAW,CAACG,IAAI,EAAEgB,OAAO,CAAC;AACzE;;AAEA;AACA;AACA;AACA,OAAO,SAASmD,iBAAiBA,CAACC,OAAO,EAAEC,SAAS,GAAGhF,UAAU,CAACO,OAAO,EAAE0E,UAAU,GAAGzE,WAAW,CAACE,MAAM,EAAE;EAC1G,OAAO,gBAAe,GAAGwE,IAAI,EAAE;IAC7B,IAAI;MACF,OAAO,MAAMH,OAAO,CAACI,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;IACxC,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdH,WAAW,CAACG,KAAK,EAAEwD,SAAS,EAAEC,UAAU,EAAE;QACxCG,QAAQ,EAAEL,OAAO,CAACM,IAAI;QACtBC,SAAS,EAAEJ;MACb,CAAC,CAAC;MACF,MAAM1D,KAAK;IACb;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAAS+D,aAAaA,CAACC,OAAO,EAAER,SAAS,GAAGhF,UAAU,CAACO,OAAO,EAAE0E,UAAU,GAAGzE,WAAW,CAACE,MAAM,EAAE;EACtG,OAAO8E,OAAO,CAACC,KAAK,CAACjE,KAAK,IAAI;IAC5BH,WAAW,CAACG,KAAK,EAAEwD,SAAS,EAAEC,UAAU,CAAC;IACzC,MAAMzD,KAAK;EACb,CAAC,CAAC;AACJ;AAEA,eAAekD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}