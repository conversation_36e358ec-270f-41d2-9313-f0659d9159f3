/**
 * 安全工具类
 * 提供数据加密、输入过滤、XSS防护等安全功能
 */

/**
 * 安全工具类
 */
export class SecurityUtils {
  constructor() {
    this.setupCSP()
  }

  /**
   * 设置内容安全策略
   */
  setupCSP() {
    // 在实际应用中，CSP应该在服务器端设置
    // 这里只是作为客户端的补充检查
    if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      console.warn('建议设置Content-Security-Policy以增强安全性')
    }
  }

  /**
   * HTML转义，防止XSS攻击
   * @param {string} str 需要转义的字符串
   * @returns {string} 转义后的字符串
   */
  escapeHtml(str) {
    if (typeof str !== 'string') return str
    
    const div = document.createElement('div')
    div.textContent = str
    return div.innerHTML
  }

  /**
   * 移除HTML标签
   * @param {string} str 包含HTML的字符串
   * @returns {string} 纯文本字符串
   */
  stripHtml(str) {
    if (typeof str !== 'string') return str
    
    const div = document.createElement('div')
    div.innerHTML = str
    return div.textContent || div.innerText || ''
  }

  /**
   * 验证和清理URL
   * @param {string} url URL字符串
   * @returns {string|null} 清理后的URL或null
   */
  sanitizeUrl(url) {
    if (typeof url !== 'string') return null
    
    try {
      const urlObj = new URL(url)
      
      // 只允许http和https协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return null
      }
      
      return urlObj.href
    } catch {
      return null
    }
  }

  /**
   * 验证邮箱地址
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否为有效邮箱
   */
  validateEmail(email) {
    if (typeof email !== 'string') return false
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  }

  /**
   * 验证IP地址
   * @param {string} ip IP地址
   * @returns {boolean} 是否为有效IP
   */
  validateIP(ip) {
    if (typeof ip !== 'string') return false
    
    // IPv4验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    if (ipv4Regex.test(ip)) return true
    
    // IPv6验证（简化版）
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    return ipv6Regex.test(ip)
  }

  /**
   * 生成随机字符串
   * @param {number} length 字符串长度
   * @param {string} charset 字符集
   * @returns {string} 随机字符串
   */
  generateRandomString(length = 16, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
    let result = ''
    const array = new Uint8Array(length)
    crypto.getRandomValues(array)
    
    for (let i = 0; i < length; i++) {
      result += charset[array[i] % charset.length]
    }
    
    return result
  }

  /**
   * 生成UUID
   * @returns {string} UUID字符串
   */
  generateUUID() {
    if (crypto.randomUUID) {
      return crypto.randomUUID()
    }
    
    // 降级方案
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 简单的字符串哈希
   * @param {string} str 要哈希的字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16)
  }

  /**
   * 检查密码强度
   * @param {string} password 密码
   * @returns {Object} 强度信息
   */
  checkPasswordSecurity(password) {
    const issues = []
    let score = 0
    
    if (!password) {
      return { score: 0, issues: ['密码不能为空'], level: 'critical' }
    }
    
    // 长度检查
    if (password.length < 8) {
      issues.push('密码长度至少8位')
    } else {
      score += 20
    }
    
    if (password.length >= 12) score += 10
    if (password.length >= 16) score += 10
    
    // 字符类型检查
    if (/[a-z]/.test(password)) score += 10
    else issues.push('缺少小写字母')
    
    if (/[A-Z]/.test(password)) score += 10
    else issues.push('缺少大写字母')
    
    if (/[0-9]/.test(password)) score += 10
    else issues.push('缺少数字')
    
    if (/[^a-zA-Z0-9]/.test(password)) score += 15
    else issues.push('缺少特殊字符')
    
    // 复杂度检查
    if (!/(.)\1{2,}/.test(password)) score += 10
    else issues.push('避免连续重复字符')
    
    // 常见密码检查
    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root']
    if (!commonPasswords.some(common => password.toLowerCase().includes(common))) {
      score += 15
    } else {
      issues.push('避免使用常见密码')
    }
    
    // 确定安全级别
    let level = 'critical'
    if (score >= 80) level = 'high'
    else if (score >= 60) level = 'medium'
    else if (score >= 40) level = 'low'
    
    return { score, issues, level }
  }

  /**
   * 输入过滤和清理
   * @param {string} input 用户输入
   * @param {Object} options 过滤选项
   * @returns {string} 清理后的输入
   */
  sanitizeInput(input, options = {}) {
    if (typeof input !== 'string') return ''
    
    const {
      maxLength = 1000,
      allowHtml = false,
      allowSpecialChars = true,
      trimWhitespace = true
    } = options
    
    let sanitized = input
    
    // 长度限制
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength)
    }
    
    // 去除首尾空白
    if (trimWhitespace) {
      sanitized = sanitized.trim()
    }
    
    // HTML处理
    if (!allowHtml) {
      sanitized = this.escapeHtml(sanitized)
    }
    
    // 特殊字符处理
    if (!allowSpecialChars) {
      sanitized = sanitized.replace(/[<>'"&]/g, '')
    }
    
    return sanitized
  }

  /**
   * 检查是否为可疑的用户代理
   * @param {string} userAgent 用户代理字符串
   * @returns {boolean} 是否可疑
   */
  isSuspiciousUserAgent(userAgent) {
    if (!userAgent) return true
    
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i
    ]
    
    return suspiciousPatterns.some(pattern => pattern.test(userAgent))
  }

  /**
   * 生成CSRF令牌
   * @returns {string} CSRF令牌
   */
  generateCSRFToken() {
    return this.generateRandomString(32)
  }

  /**
   * 验证CSRF令牌
   * @param {string} token 提交的令牌
   * @param {string} expectedToken 期望的令牌
   * @returns {boolean} 是否有效
   */
  validateCSRFToken(token, expectedToken) {
    return token === expectedToken && token.length === 32
  }

  /**
   * 检查请求频率限制
   * @param {string} identifier 标识符（如IP地址）
   * @param {number} maxRequests 最大请求数
   * @param {number} timeWindow 时间窗口（毫秒）
   * @returns {boolean} 是否允许请求
   */
  checkRateLimit(identifier, maxRequests = 100, timeWindow = 60000) {
    const now = Date.now()
    const key = `rateLimit_${identifier}`
    
    let requests = JSON.parse(localStorage.getItem(key) || '[]')
    
    // 清理过期的请求记录
    requests = requests.filter(timestamp => now - timestamp < timeWindow)
    
    // 检查是否超过限制
    if (requests.length >= maxRequests) {
      return false
    }
    
    // 记录当前请求
    requests.push(now)
    localStorage.setItem(key, JSON.stringify(requests))
    
    return true
  }

  /**
   * 安全的JSON解析
   * @param {string} jsonString JSON字符串
   * @returns {Object|null} 解析结果或null
   */
  safeJSONParse(jsonString) {
    try {
      if (typeof jsonString !== 'string') return null
      
      // 检查JSON字符串长度
      if (jsonString.length > 1024 * 1024) { // 1MB限制
        console.warn('JSON字符串过大')
        return null
      }
      
      return JSON.parse(jsonString)
    } catch {
      return null
    }
  }

  /**
   * 清理对象中的敏感信息
   * @param {Object} obj 对象
   * @param {Array} sensitiveKeys 敏感字段名
   * @returns {Object} 清理后的对象
   */
  sanitizeObject(obj, sensitiveKeys = ['password', 'token', 'secret', 'key']) {
    if (!obj || typeof obj !== 'object') return obj
    
    const cleaned = { ...obj }
    
    sensitiveKeys.forEach(key => {
      if (key in cleaned) {
        cleaned[key] = '[REDACTED]'
      }
    })
    
    return cleaned
  }
}

// 创建全局安全工具实例
export const security = new SecurityUtils()

// 便捷函数导出
export const {
  escapeHtml,
  stripHtml,
  sanitizeUrl,
  validateEmail,
  validateIP,
  generateRandomString,
  generateUUID,
  simpleHash,
  checkPasswordSecurity,
  sanitizeInput,
  generateCSRFToken,
  validateCSRFToken,
  checkRateLimit,
  safeJSONParse,
  sanitizeObject
} = security

export default security
