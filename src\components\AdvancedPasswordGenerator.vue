<template>
  <div class="password-generator">
    <!-- 生成的密码显示 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        生成的密码
      </label>
      <div class="relative">
        <input
          :type="showPassword ? 'text' : 'password'"
          :value="generatedPassword"
          readonly
          class="w-full px-4 py-3 pr-20 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white"
          :class="passwordStrengthClass"
        />
        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
          <button
            @click="togglePasswordVisibility"
            class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            type="button"
          >
            <font-awesome-icon :icon="['fas', showPassword ? 'eye-slash' : 'eye']" />
          </button>
          <button
            @click="copyPassword"
            class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            type="button"
          >
            <font-awesome-icon :icon="['fas', copied ? 'check' : 'copy']" :class="{ 'text-green-500': copied }" />
          </button>
          <button
            @click="generatePassword"
            class="p-2 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
            type="button"
          >
            <font-awesome-icon :icon="['fas', 'sync-alt']" :class="{ 'animate-spin': generating }" />
          </button>
        </div>
      </div>
      
      <!-- 密码强度指示器 -->
      <div class="mt-3">
        <PasswordStrengthMeter
          :password="generatedPassword"
          :show-details="true"
          :show-suggestions="true"
        />
      </div>
    </div>

    <!-- 密码选项 -->
    <div class="space-y-4">
      <!-- 密码长度 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          密码长度: {{ options.length }}
        </label>
        <input
          type="range"
          v-model.number="options.length"
          min="8"
          max="64"
          class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          @input="generatePassword"
        />
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>8</span>
          <span>64</span>
        </div>
      </div>

      <!-- 字符类型选项 -->
      <div class="grid grid-cols-2 gap-4">
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="options.includeUppercase"
            @change="generatePassword"
            class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300">大写字母 (A-Z)</span>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="options.includeLowercase"
            @change="generatePassword"
            class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300">小写字母 (a-z)</span>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="options.includeNumbers"
            @change="generatePassword"
            class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300">数字 (0-9)</span>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="options.includeSymbols"
            @change="generatePassword"
            class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300">特殊字符 (!@#$)</span>
        </label>
      </div>

      <!-- 高级选项 -->
      <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">高级选项</h4>
        
        <div class="space-y-3">
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              v-model="options.excludeSimilar"
              @change="generatePassword"
              class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">排除相似字符 (0, O, l, 1, I)</span>
          </label>
          
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              v-model="options.excludeAmbiguous"
              @change="generatePassword"
              class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">排除歧义字符 ({{ '{' }} {{ '}' }} [ ] ( ) / \ ' " ~ , ; . &lt; &gt;)</span>
          </label>
          
          <div class="flex items-center space-x-3">
            <label class="text-sm text-gray-700 dark:text-gray-300">自定义字符:</label>
            <input
              type="text"
              v-model="options.customChars"
              @input="generatePassword"
              placeholder="添加自定义字符"
              class="flex-1 px-3 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:text-white"
            />
          </div>
        </div>
      </div>

      <!-- 预设模板 -->
      <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">快速模板</h4>
        <div class="grid grid-cols-2 gap-2">
          <button
            v-for="template in templates"
            :key="template.name"
            @click="applyTemplate(template)"
            class="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
          >
            {{ template.name }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PasswordStrengthMeter from './PasswordStrengthMeter.vue'
import { generateSecurePassword, checkPasswordStrength } from '@/utils/passwordUtils'
import { handleError, ErrorTypes, ErrorLevels } from '@/utils/errorHandler'

export default {
  name: 'AdvancedPasswordGenerator',
  components: {
    PasswordStrengthMeter
  },
  emits: ['password-generated'],
  props: {
    initialOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      generatedPassword: '',
      showPassword: false,
      copied: false,
      generating: false,
      options: {
        length: 16,
        includeUppercase: true,
        includeLowercase: true,
        includeNumbers: true,
        includeSymbols: true,
        excludeSimilar: false,
        excludeAmbiguous: false,
        customChars: ''
      },
      templates: [
        {
          name: '高强度',
          options: { length: 20, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: true }
        },
        {
          name: '标准',
          options: { length: 12, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: false }
        },
        {
          name: '简单',
          options: { length: 8, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: false }
        },
        {
          name: '数字密码',
          options: { length: 6, includeUppercase: false, includeLowercase: false, includeNumbers: true, includeSymbols: false }
        }
      ]
    }
  },
  computed: {
    passwordStrength() {
      return checkPasswordStrength(this.generatedPassword)
    },
    passwordStrengthClass() {
      const strength = this.passwordStrength.strength
      if (strength >= 4) return 'border-green-500 dark:border-green-400'
      if (strength >= 3) return 'border-yellow-500 dark:border-yellow-400'
      if (strength >= 2) return 'border-orange-500 dark:border-orange-400'
      return 'border-red-500 dark:border-red-400'
    }
  },
  mounted() {
    // 应用初始选项
    Object.assign(this.options, this.initialOptions)
    this.generatePassword()
  },
  methods: {
    generatePassword() {
      this.generating = true

      setTimeout(() => {
        try {
          const passwordOptions = {
            length: this.options.length,
            includeUppercase: this.options.includeUppercase,
            includeLowercase: this.options.includeLowercase,
            includeNumbers: this.options.includeNumbers,
            includeSpecial: this.options.includeSymbols,
            excludeSimilar: this.options.excludeSimilar,
            excludeAmbiguous: this.options.excludeAmbiguous,
            customChars: this.options.customChars
          }

          this.generatedPassword = generateSecurePassword(passwordOptions)

          // 发出事件
          this.$emit('password-generated', this.generatedPassword)
        } catch (error) {
          handleError(error, ErrorTypes.CLIENT, ErrorLevels.MEDIUM, {
            component: 'AdvancedPasswordGenerator',
            method: 'generatePassword',
            options: this.options
          })
          this.generatedPassword = ''
        } finally {
          this.generating = false
        }
      }, 100)
    },
    
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    },
    
    async copyPassword() {
      try {
        await navigator.clipboard.writeText(this.generatedPassword)
        this.copied = true
        setTimeout(() => {
          this.copied = false
        }, 2000)
      } catch (err) {
        handleError(err, ErrorTypes.CLIENT, ErrorLevels.LOW, {
          component: 'AdvancedPasswordGenerator',
          method: 'copyPassword'
        })
        // 降级方案：使用传统的复制方法
        this.fallbackCopyPassword()
      }
    },

    fallbackCopyPassword() {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = this.generatedPassword
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.copied = true
        setTimeout(() => {
          this.copied = false
        }, 2000)
      } catch (err) {
        handleError(err, ErrorTypes.CLIENT, ErrorLevels.MEDIUM, {
          component: 'AdvancedPasswordGenerator',
          method: 'fallbackCopyPassword'
        })
      }
    },

    applyTemplate(template) {
      try {
        Object.assign(this.options, template.options)
        this.generatePassword()
      } catch (error) {
        handleError(error, ErrorTypes.CLIENT, ErrorLevels.LOW, {
          component: 'AdvancedPasswordGenerator',
          method: 'applyTemplate',
          template: template.name
        })
      }
    }
  }
}
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
