{"ast": null, "code": "/**\r\n * 密码管理工具类\r\n */\n\n/**\r\n * 生成随机密码\r\n * @param {number} length 密码长度\r\n * @param {object} options 密码生成选项\r\n * @returns {string} 生成的密码\r\n */\nexport function generatePassword(length = 12, options = {}) {\n  const {\n    includeUpper = true,\n    includeLower = true,\n    includeNumbers = true,\n    includeSpecial = true\n  } = options;\n\n  // 字符集\n  const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  const lowerChars = 'abcdefghijklmnopqrstuvwxyz';\n  const numberChars = '0123456789';\n  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';\n\n  // 构建字符集\n  let chars = '';\n  if (includeUpper) chars += upperChars;\n  if (includeLower) chars += lowerChars;\n  if (includeNumbers) chars += numberChars;\n  if (includeSpecial) chars += specialChars;\n\n  // 确保至少有一个字符集\n  if (chars.length === 0) {\n    chars = lowerChars + numberChars;\n  }\n\n  // 生成密码\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    const randomIndex = Math.floor(Math.random() * chars.length);\n    password += chars[randomIndex];\n  }\n\n  // 确保密码满足要求\n  const hasUpper = includeUpper ? /[A-Z]/.test(password) : true;\n  const hasLower = includeLower ? /[a-z]/.test(password) : true;\n  const hasNumbers = includeNumbers ? /\\d/.test(password) : true;\n  const hasSpecial = includeSpecial ? /[^A-Za-z0-9]/.test(password) : true;\n\n  // 如果不满足要求，重新生成\n  if (!hasUpper || !hasLower || !hasNumbers || !hasSpecial) {\n    return generatePassword(length, options);\n  }\n  return password;\n}\n\n/**\r\n * 计算密码强度\r\n * @param {string} password 密码\r\n * @returns {number} 强度得分 (0-4)\r\n */\nexport function calculatePasswordStrength(password) {\n  if (!password) return 0;\n  let score = 0;\n\n  // 长度评分\n  if (password.length >= 8) score += 1;\n  if (password.length >= 12) score += 1;\n\n  // 字符多样性评分\n  if (/[A-Z]/.test(password)) score += 0.5;\n  if (/[a-z]/.test(password)) score += 0.5;\n  if (/\\d/.test(password)) score += 0.5;\n  if (/[^A-Za-z0-9]/.test(password)) score += 0.5;\n\n  // 复杂度评分\n  const variety = (/[A-Z]/.test(password) ? 1 : 0) + (/[a-z]/.test(password) ? 1 : 0) + (/\\d/.test(password) ? 1 : 0) + (/[^A-Za-z0-9]/.test(password) ? 1 : 0);\n  if (variety >= 3) score += 1;\n\n  // 返回整数得分，最高4分\n  return Math.min(4, Math.floor(score));\n}\n\n/**\r\n * 检查密码是否符合策略要求\r\n * @param {string} password 密码\r\n * @param {object} policy 密码策略\r\n * @returns {object} 检查结果 { valid: boolean, errors: string[] }\r\n */\nexport function validatePasswordAgainstPolicy(password, policy) {\n  const errors = [];\n\n  // 检查长度\n  if (password.length < policy.minLength) {\n    errors.push(`密码长度必须至少为 ${policy.minLength} 位`);\n  }\n\n  // 检查大写字母\n  if (policy.requireUppercase && !/[A-Z]/.test(password)) {\n    errors.push('密码必须包含至少一个大写字母');\n  }\n\n  // 检查小写字母\n  if (policy.requireLowercase && !/[a-z]/.test(password)) {\n    errors.push('密码必须包含至少一个小写字母');\n  }\n\n  // 检查数字\n  if (policy.requireNumbers && !/\\d/.test(password)) {\n    errors.push('密码必须包含至少一个数字');\n  }\n\n  // 检查特殊字符\n  if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\n    errors.push('密码必须包含至少一个特殊字符');\n  }\n\n  // 检查用户名包含\n  if (policy.forbidUsername && policy.username) {\n    const username = policy.username.toLowerCase();\n    if (password.toLowerCase().includes(username)) {\n      errors.push('密码不能包含用户名');\n    }\n  }\n  return {\n    valid: errors.length === 0,\n    errors\n  };\n}\n\n/**\r\n * 获取密码强度描述\r\n * @param {number} strength 密码强度 (0-4)\r\n * @returns {object} 描述信息 { text: string, colorClass: string }\r\n */\nexport function getPasswordStrengthInfo(strength) {\n  switch (strength) {\n    case 0:\n      return {\n        text: '请输入密码',\n        colorClass: 'text-gray-500'\n      };\n    case 1:\n      return {\n        text: '弱',\n        colorClass: 'text-red-600'\n      };\n    case 2:\n      return {\n        text: '中等',\n        colorClass: 'text-yellow-600'\n      };\n    case 3:\n      return {\n        text: '强',\n        colorClass: 'text-green-600'\n      };\n    case 4:\n      return {\n        text: '非常强',\n        colorClass: 'text-green-600'\n      };\n    default:\n      return {\n        text: '',\n        colorClass: ''\n      };\n  }\n}\n\n/**\r\n * 计算密码过期状态\r\n * @param {string} lastChangeDate 上次修改日期\r\n * @param {number} expiryDays 过期天数\r\n * @returns {object} 过期状态信息\r\n */\nexport function calculatePasswordExpiry(lastChangeDate, expiryDays) {\n  if (!lastChangeDate || !expiryDays) {\n    return {\n      status: 'unknown',\n      daysRemaining: 0,\n      text: '未知'\n    };\n  }\n  const lastChange = new Date(lastChangeDate);\n  const now = new Date();\n  const expiryDate = new Date(lastChange);\n  expiryDate.setDate(expiryDate.getDate() + expiryDays);\n  const diffTime = expiryDate - now;\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  let status = 'normal';\n  let text = '';\n  if (diffDays < 0) {\n    status = 'expired';\n    text = `已过期${Math.abs(diffDays)}天`;\n  } else if (diffDays === 0) {\n    status = 'expiring';\n    text = '今天过期';\n  } else if (diffDays <= 3) {\n    status = 'critical';\n    text = `${diffDays}天后过期`;\n  } else if (diffDays <= 7) {\n    status = 'warning';\n    text = `${diffDays}天后过期`;\n  } else if (diffDays <= 30) {\n    status = 'notice';\n    text = `${diffDays}天后过期`;\n  } else {\n    status = 'normal';\n    text = `${diffDays}天后过期`;\n  }\n  return {\n    status,\n    daysRemaining: diffDays,\n    text,\n    expiryDate\n  };\n}\n\n/**\r\n * 生成密码建议\r\n * @param {string} password 当前密码\r\n * @param {object} policy 密码策略\r\n * @returns {array} 建议列表\r\n */\nexport function generatePasswordSuggestions(password, policy = {}) {\n  const suggestions = [];\n  if (!password) {\n    suggestions.push('请输入密码');\n    return suggestions;\n  }\n\n  // 长度建议\n  if (password.length < (policy.minLength || 8)) {\n    suggestions.push(`密码长度至少需要${policy.minLength || 8}位`);\n  } else if (password.length < 12) {\n    suggestions.push('建议使用12位以上的密码以提高安全性');\n  }\n\n  // 字符类型建议\n  if (policy.requireUppercase && !/[A-Z]/.test(password)) {\n    suggestions.push('添加大写字母');\n  }\n  if (policy.requireLowercase && !/[a-z]/.test(password)) {\n    suggestions.push('添加小写字母');\n  }\n  if (policy.requireNumbers && !/\\d/.test(password)) {\n    suggestions.push('添加数字');\n  }\n  if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\n    suggestions.push('添加特殊字符');\n  }\n\n  // 安全性建议\n  if (/(.)\\1{2,}/.test(password)) {\n    suggestions.push('避免连续重复的字符');\n  }\n  if (/123|abc|qwe/i.test(password)) {\n    suggestions.push('避免使用连续的字符序列');\n  }\n\n  // 常见密码检查\n  const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root'];\n  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {\n    suggestions.push('避免使用常见的密码模式');\n  }\n  if (suggestions.length === 0) {\n    suggestions.push('密码强度良好');\n  }\n  return suggestions;\n}\n\n/**\r\n * 格式化密码显示\r\n * @param {string} password 密码\r\n * @param {boolean} masked 是否遮罩显示\r\n * @param {number} visibleChars 显示的字符数\r\n * @returns {string} 格式化后的密码\r\n */\nexport function formatPasswordDisplay(password, masked = true, visibleChars = 0) {\n  if (!password) return '';\n  if (!masked) {\n    return password;\n  }\n  if (visibleChars > 0 && visibleChars < password.length) {\n    const visible = password.substring(0, visibleChars);\n    const hidden = '•'.repeat(password.length - visibleChars);\n    return visible + hidden;\n  }\n  return '•'.repeat(password.length);\n}\n\n/**\r\n * 检查密码是否为常见弱密码\r\n * @param {string} password 密码\r\n * @returns {boolean} 是否为弱密码\r\n */\nexport function isWeakPassword(password) {\n  if (!password) return true;\n  const weakPatterns = [/^123+$/,\n  // 纯数字序列\n  /^abc+$/i,\n  // 纯字母序列\n  /^(.)\\1+$/,\n  // 重复字符\n  /^password/i,\n  // 包含password\n  /^admin/i,\n  // 包含admin\n  /^root/i,\n  // 包含root\n  /^qwerty/i,\n  // 键盘序列\n  /^12345/,\n  // 数字序列\n  /^abcde/i // 字母序列\n  ];\n  return weakPatterns.some(pattern => pattern.test(password)) || password.length < 6;\n}\n\n/**\r\n * 生成密码历史记录\r\n * @param {string} newPassword 新密码\r\n * @param {array} history 历史记录\r\n * @param {number} maxHistory 最大历史记录数\r\n * @returns {array} 更新后的历史记录\r\n */\nexport function updatePasswordHistory(newPassword, history = [], maxHistory = 5) {\n  const newRecord = {\n    password: newPassword,\n    timestamp: new Date().toISOString(),\n    id: Date.now()\n  };\n  const updatedHistory = [newRecord, ...history];\n  return updatedHistory.slice(0, maxHistory);\n}\n\n/**\r\n * 检查密码是否在历史记录中\r\n * @param {string} password 密码\r\n * @param {array} history 历史记录\r\n * @returns {boolean} 是否存在于历史记录中\r\n */\nexport function isPasswordInHistory(password, history = []) {\n  return history.some(record => record.password === password);\n}\n\n/**\r\n * 生成密码复杂度报告\r\n * @param {string} password 密码\r\n * @returns {object} 复杂度报告\r\n */\nexport function generatePasswordComplexityReport(password) {\n  if (!password) {\n    return {\n      score: 0,\n      maxScore: 100,\n      details: {\n        length: {\n          score: 0,\n          max: 25,\n          description: '长度'\n        },\n        variety: {\n          score: 0,\n          max: 25,\n          description: '字符多样性'\n        },\n        uniqueness: {\n          score: 0,\n          max: 25,\n          description: '唯一性'\n        },\n        patterns: {\n          score: 0,\n          max: 25,\n          description: '模式复杂度'\n        }\n      }\n    };\n  }\n  const details = {\n    length: {\n      score: 0,\n      max: 25,\n      description: '长度'\n    },\n    variety: {\n      score: 0,\n      max: 25,\n      description: '字符多样性'\n    },\n    uniqueness: {\n      score: 0,\n      max: 25,\n      description: '唯一性'\n    },\n    patterns: {\n      score: 0,\n      max: 25,\n      description: '模式复杂度'\n    }\n  };\n\n  // 长度评分\n  if (password.length >= 8) details.length.score += 10;\n  if (password.length >= 12) details.length.score += 10;\n  if (password.length >= 16) details.length.score += 5;\n\n  // 字符多样性评分\n  if (/[a-z]/.test(password)) details.variety.score += 6;\n  if (/[A-Z]/.test(password)) details.variety.score += 6;\n  if (/\\d/.test(password)) details.variety.score += 6;\n  if (/[^A-Za-z0-9]/.test(password)) details.variety.score += 7;\n\n  // 唯一性评分（避免常见密码）\n  if (!isWeakPassword(password)) details.uniqueness.score += 25;\n\n  // 模式复杂度评分\n  if (!/(.)\\1{2,}/.test(password)) details.patterns.score += 8; // 无重复字符\n  if (!/123|abc|qwe/i.test(password)) details.patterns.score += 8; // 无序列\n  if (password.length > 0) details.patterns.score += Math.min(9, password.length); // 基础分\n\n  const totalScore = Object.values(details).reduce((sum, item) => sum + item.score, 0);\n  return {\n    score: totalScore,\n    maxScore: 100,\n    details\n  };\n}", "map": {"version": 3, "names": ["generatePassword", "length", "options", "includeUpper", "<PERSON><PERSON><PERSON><PERSON>", "includeNumbers", "includeSpecial", "upperChars", "lowerChars", "numberChars", "specialChars", "chars", "password", "i", "randomIndex", "Math", "floor", "random", "has<PERSON>pper", "test", "<PERSON><PERSON><PERSON><PERSON>", "hasNumbers", "hasSpecial", "calculatePasswordStrength", "score", "variety", "min", "validatePasswordAgainstPolicy", "policy", "errors", "<PERSON><PERSON><PERSON><PERSON>", "push", "requireUppercase", "requireLowercase", "requireNumbers", "requireSpecial", "forbidUsername", "username", "toLowerCase", "includes", "valid", "getPasswordStrengthInfo", "strength", "text", "colorClass", "calculatePasswordExpiry", "lastChangeDate", "expiryDays", "status", "daysRemaining", "lastChange", "Date", "now", "expiryDate", "setDate", "getDate", "diffTime", "diffDays", "ceil", "abs", "generatePasswordSuggestions", "suggestions", "commonPasswords", "some", "common", "formatPasswordDisplay", "masked", "visibleChars", "visible", "substring", "hidden", "repeat", "isWeakPassword", "weakPatterns", "pattern", "updatePasswordHistory", "newPassword", "history", "maxHist<PERSON>", "newRecord", "timestamp", "toISOString", "id", "updatedHistory", "slice", "isPasswordInHistory", "record", "generatePasswordComplexityReport", "maxScore", "details", "max", "description", "uniqueness", "patterns", "totalScore", "Object", "values", "reduce", "sum", "item"], "sources": ["D:/demo/ooo/pass/src/utils/passwordUtils.js"], "sourcesContent": ["/**\r\n * 密码管理工具类\r\n */\r\n\r\n/**\r\n * 生成随机密码\r\n * @param {number} length 密码长度\r\n * @param {object} options 密码生成选项\r\n * @returns {string} 生成的密码\r\n */\r\nexport function generatePassword(length = 12, options = {}) {\r\n    const {\r\n        includeUpper = true,\r\n        includeLower = true,\r\n        includeNumbers = true,\r\n        includeSpecial = true\r\n    } = options\r\n\r\n    // 字符集\r\n    const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'\r\n    const lowerChars = 'abcdefghijklmnopqrstuvwxyz'\r\n    const numberChars = '0123456789'\r\n    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'\r\n\r\n    // 构建字符集\r\n    let chars = ''\r\n    if (includeUpper) chars += upperChars\r\n    if (includeLower) chars += lowerChars\r\n    if (includeNumbers) chars += numberChars\r\n    if (includeSpecial) chars += specialChars\r\n\r\n    // 确保至少有一个字符集\r\n    if (chars.length === 0) {\r\n        chars = lowerChars + numberChars\r\n    }\r\n\r\n    // 生成密码\r\n    let password = ''\r\n    for (let i = 0; i < length; i++) {\r\n        const randomIndex = Math.floor(Math.random() * chars.length)\r\n        password += chars[randomIndex]\r\n    }\r\n\r\n    // 确保密码满足要求\r\n    const hasUpper = includeUpper ? /[A-Z]/.test(password) : true\r\n    const hasLower = includeLower ? /[a-z]/.test(password) : true\r\n    const hasNumbers = includeNumbers ? /\\d/.test(password) : true\r\n    const hasSpecial = includeSpecial ? /[^A-Za-z0-9]/.test(password) : true\r\n\r\n    // 如果不满足要求，重新生成\r\n    if (!hasUpper || !hasLower || !hasNumbers || !hasSpecial) {\r\n        return generatePassword(length, options)\r\n    }\r\n\r\n    return password\r\n}\r\n\r\n/**\r\n * 计算密码强度\r\n * @param {string} password 密码\r\n * @returns {number} 强度得分 (0-4)\r\n */\r\nexport function calculatePasswordStrength(password) {\r\n    if (!password) return 0\r\n\r\n    let score = 0\r\n\r\n    // 长度评分\r\n    if (password.length >= 8) score += 1\r\n    if (password.length >= 12) score += 1\r\n\r\n    // 字符多样性评分\r\n    if (/[A-Z]/.test(password)) score += 0.5\r\n    if (/[a-z]/.test(password)) score += 0.5\r\n    if (/\\d/.test(password)) score += 0.5\r\n    if (/[^A-Za-z0-9]/.test(password)) score += 0.5\r\n\r\n    // 复杂度评分\r\n    const variety = (/[A-Z]/.test(password) ? 1 : 0)\r\n        + (/[a-z]/.test(password) ? 1 : 0)\r\n        + (/\\d/.test(password) ? 1 : 0)\r\n        + (/[^A-Za-z0-9]/.test(password) ? 1 : 0)\r\n\r\n    if (variety >= 3) score += 1\r\n\r\n    // 返回整数得分，最高4分\r\n    return Math.min(4, Math.floor(score))\r\n}\r\n\r\n/**\r\n * 检查密码是否符合策略要求\r\n * @param {string} password 密码\r\n * @param {object} policy 密码策略\r\n * @returns {object} 检查结果 { valid: boolean, errors: string[] }\r\n */\r\nexport function validatePasswordAgainstPolicy(password, policy) {\r\n    const errors = []\r\n\r\n    // 检查长度\r\n    if (password.length < policy.minLength) {\r\n        errors.push(`密码长度必须至少为 ${policy.minLength} 位`)\r\n    }\r\n\r\n    // 检查大写字母\r\n    if (policy.requireUppercase && !/[A-Z]/.test(password)) {\r\n        errors.push('密码必须包含至少一个大写字母')\r\n    }\r\n\r\n    // 检查小写字母\r\n    if (policy.requireLowercase && !/[a-z]/.test(password)) {\r\n        errors.push('密码必须包含至少一个小写字母')\r\n    }\r\n\r\n    // 检查数字\r\n    if (policy.requireNumbers && !/\\d/.test(password)) {\r\n        errors.push('密码必须包含至少一个数字')\r\n    }\r\n\r\n    // 检查特殊字符\r\n    if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\r\n        errors.push('密码必须包含至少一个特殊字符')\r\n    }\r\n\r\n    // 检查用户名包含\r\n    if (policy.forbidUsername && policy.username) {\r\n        const username = policy.username.toLowerCase()\r\n        if (password.toLowerCase().includes(username)) {\r\n            errors.push('密码不能包含用户名')\r\n        }\r\n    }\r\n\r\n    return {\r\n        valid: errors.length === 0,\r\n        errors\r\n    }\r\n}\r\n\r\n/**\r\n * 获取密码强度描述\r\n * @param {number} strength 密码强度 (0-4)\r\n * @returns {object} 描述信息 { text: string, colorClass: string }\r\n */\r\nexport function getPasswordStrengthInfo(strength) {\r\n    switch (strength) {\r\n        case 0:\r\n            return { text: '请输入密码', colorClass: 'text-gray-500' }\r\n        case 1:\r\n            return { text: '弱', colorClass: 'text-red-600' }\r\n        case 2:\r\n            return { text: '中等', colorClass: 'text-yellow-600' }\r\n        case 3:\r\n            return { text: '强', colorClass: 'text-green-600' }\r\n        case 4:\r\n            return { text: '非常强', colorClass: 'text-green-600' }\r\n        default:\r\n            return { text: '', colorClass: '' }\r\n    }\r\n}\r\n\r\n/**\r\n * 计算密码过期状态\r\n * @param {string} lastChangeDate 上次修改日期\r\n * @param {number} expiryDays 过期天数\r\n * @returns {object} 过期状态信息\r\n */\r\nexport function calculatePasswordExpiry(lastChangeDate, expiryDays) {\r\n    if (!lastChangeDate || !expiryDays) {\r\n        return { status: 'unknown', daysRemaining: 0, text: '未知' }\r\n    }\r\n\r\n    const lastChange = new Date(lastChangeDate)\r\n    const now = new Date()\r\n    const expiryDate = new Date(lastChange)\r\n    expiryDate.setDate(expiryDate.getDate() + expiryDays)\r\n\r\n    const diffTime = expiryDate - now\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\r\n\r\n    let status = 'normal'\r\n    let text = ''\r\n\r\n    if (diffDays < 0) {\r\n        status = 'expired'\r\n        text = `已过期${Math.abs(diffDays)}天`\r\n    } else if (diffDays === 0) {\r\n        status = 'expiring'\r\n        text = '今天过期'\r\n    } else if (diffDays <= 3) {\r\n        status = 'critical'\r\n        text = `${diffDays}天后过期`\r\n    } else if (diffDays <= 7) {\r\n        status = 'warning'\r\n        text = `${diffDays}天后过期`\r\n    } else if (diffDays <= 30) {\r\n        status = 'notice'\r\n        text = `${diffDays}天后过期`\r\n    } else {\r\n        status = 'normal'\r\n        text = `${diffDays}天后过期`\r\n    }\r\n\r\n    return { status, daysRemaining: diffDays, text, expiryDate }\r\n}\r\n\r\n/**\r\n * 生成密码建议\r\n * @param {string} password 当前密码\r\n * @param {object} policy 密码策略\r\n * @returns {array} 建议列表\r\n */\r\nexport function generatePasswordSuggestions(password, policy = {}) {\r\n    const suggestions = []\r\n\r\n    if (!password) {\r\n        suggestions.push('请输入密码')\r\n        return suggestions\r\n    }\r\n\r\n    // 长度建议\r\n    if (password.length < (policy.minLength || 8)) {\r\n        suggestions.push(`密码长度至少需要${policy.minLength || 8}位`)\r\n    } else if (password.length < 12) {\r\n        suggestions.push('建议使用12位以上的密码以提高安全性')\r\n    }\r\n\r\n    // 字符类型建议\r\n    if (policy.requireUppercase && !/[A-Z]/.test(password)) {\r\n        suggestions.push('添加大写字母')\r\n    }\r\n    if (policy.requireLowercase && !/[a-z]/.test(password)) {\r\n        suggestions.push('添加小写字母')\r\n    }\r\n    if (policy.requireNumbers && !/\\d/.test(password)) {\r\n        suggestions.push('添加数字')\r\n    }\r\n    if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\r\n        suggestions.push('添加特殊字符')\r\n    }\r\n\r\n    // 安全性建议\r\n    if (/(.)\\1{2,}/.test(password)) {\r\n        suggestions.push('避免连续重复的字符')\r\n    }\r\n    if (/123|abc|qwe/i.test(password)) {\r\n        suggestions.push('避免使用连续的字符序列')\r\n    }\r\n\r\n    // 常见密码检查\r\n    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root']\r\n    if (commonPasswords.some(common => password.toLowerCase().includes(common))) {\r\n        suggestions.push('避免使用常见的密码模式')\r\n    }\r\n\r\n    if (suggestions.length === 0) {\r\n        suggestions.push('密码强度良好')\r\n    }\r\n\r\n    return suggestions\r\n}\r\n\r\n/**\r\n * 格式化密码显示\r\n * @param {string} password 密码\r\n * @param {boolean} masked 是否遮罩显示\r\n * @param {number} visibleChars 显示的字符数\r\n * @returns {string} 格式化后的密码\r\n */\r\nexport function formatPasswordDisplay(password, masked = true, visibleChars = 0) {\r\n    if (!password) return ''\r\n\r\n    if (!masked) {\r\n        return password\r\n    }\r\n\r\n    if (visibleChars > 0 && visibleChars < password.length) {\r\n        const visible = password.substring(0, visibleChars)\r\n        const hidden = '•'.repeat(password.length - visibleChars)\r\n        return visible + hidden\r\n    }\r\n\r\n    return '•'.repeat(password.length)\r\n}\r\n\r\n/**\r\n * 检查密码是否为常见弱密码\r\n * @param {string} password 密码\r\n * @returns {boolean} 是否为弱密码\r\n */\r\nexport function isWeakPassword(password) {\r\n    if (!password) return true\r\n\r\n    const weakPatterns = [\r\n        /^123+$/,           // 纯数字序列\r\n        /^abc+$/i,          // 纯字母序列\r\n        /^(.)\\1+$/,         // 重复字符\r\n        /^password/i,       // 包含password\r\n        /^admin/i,          // 包含admin\r\n        /^root/i,           // 包含root\r\n        /^qwerty/i,         // 键盘序列\r\n        /^12345/,           // 数字序列\r\n        /^abcde/i           // 字母序列\r\n    ]\r\n\r\n    return weakPatterns.some(pattern => pattern.test(password)) || password.length < 6\r\n}\r\n\r\n/**\r\n * 生成密码历史记录\r\n * @param {string} newPassword 新密码\r\n * @param {array} history 历史记录\r\n * @param {number} maxHistory 最大历史记录数\r\n * @returns {array} 更新后的历史记录\r\n */\r\nexport function updatePasswordHistory(newPassword, history = [], maxHistory = 5) {\r\n    const newRecord = {\r\n        password: newPassword,\r\n        timestamp: new Date().toISOString(),\r\n        id: Date.now()\r\n    }\r\n\r\n    const updatedHistory = [newRecord, ...history]\r\n    return updatedHistory.slice(0, maxHistory)\r\n}\r\n\r\n/**\r\n * 检查密码是否在历史记录中\r\n * @param {string} password 密码\r\n * @param {array} history 历史记录\r\n * @returns {boolean} 是否存在于历史记录中\r\n */\r\nexport function isPasswordInHistory(password, history = []) {\r\n    return history.some(record => record.password === password)\r\n}\r\n\r\n/**\r\n * 生成密码复杂度报告\r\n * @param {string} password 密码\r\n * @returns {object} 复杂度报告\r\n */\r\nexport function generatePasswordComplexityReport(password) {\r\n    if (!password) {\r\n        return {\r\n            score: 0,\r\n            maxScore: 100,\r\n            details: {\r\n                length: { score: 0, max: 25, description: '长度' },\r\n                variety: { score: 0, max: 25, description: '字符多样性' },\r\n                uniqueness: { score: 0, max: 25, description: '唯一性' },\r\n                patterns: { score: 0, max: 25, description: '模式复杂度' }\r\n            }\r\n        }\r\n    }\r\n\r\n    const details = {\r\n        length: { score: 0, max: 25, description: '长度' },\r\n        variety: { score: 0, max: 25, description: '字符多样性' },\r\n        uniqueness: { score: 0, max: 25, description: '唯一性' },\r\n        patterns: { score: 0, max: 25, description: '模式复杂度' }\r\n    }\r\n\r\n    // 长度评分\r\n    if (password.length >= 8) details.length.score += 10\r\n    if (password.length >= 12) details.length.score += 10\r\n    if (password.length >= 16) details.length.score += 5\r\n\r\n    // 字符多样性评分\r\n    if (/[a-z]/.test(password)) details.variety.score += 6\r\n    if (/[A-Z]/.test(password)) details.variety.score += 6\r\n    if (/\\d/.test(password)) details.variety.score += 6\r\n    if (/[^A-Za-z0-9]/.test(password)) details.variety.score += 7\r\n\r\n    // 唯一性评分（避免常见密码）\r\n    if (!isWeakPassword(password)) details.uniqueness.score += 25\r\n\r\n    // 模式复杂度评分\r\n    if (!/(.)\\1{2,}/.test(password)) details.patterns.score += 8  // 无重复字符\r\n    if (!/123|abc|qwe/i.test(password)) details.patterns.score += 8  // 无序列\r\n    if (password.length > 0) details.patterns.score += Math.min(9, password.length)  // 基础分\r\n\r\n    const totalScore = Object.values(details).reduce((sum, item) => sum + item.score, 0)\r\n\r\n    return {\r\n        score: totalScore,\r\n        maxScore: 100,\r\n        details\r\n    }\r\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,MAAM,GAAG,EAAE,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACxD,MAAM;IACFC,YAAY,GAAG,IAAI;IACnBC,YAAY,GAAG,IAAI;IACnBC,cAAc,GAAG,IAAI;IACrBC,cAAc,GAAG;EACrB,CAAC,GAAGJ,OAAO;;EAEX;EACA,MAAMK,UAAU,GAAG,4BAA4B;EAC/C,MAAMC,UAAU,GAAG,4BAA4B;EAC/C,MAAMC,WAAW,GAAG,YAAY;EAChC,MAAMC,YAAY,GAAG,4BAA4B;;EAEjD;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIR,YAAY,EAAEQ,KAAK,IAAIJ,UAAU;EACrC,IAAIH,YAAY,EAAEO,KAAK,IAAIH,UAAU;EACrC,IAAIH,cAAc,EAAEM,KAAK,IAAIF,WAAW;EACxC,IAAIH,cAAc,EAAEK,KAAK,IAAID,YAAY;;EAEzC;EACA,IAAIC,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE;IACpBU,KAAK,GAAGH,UAAU,GAAGC,WAAW;EACpC;;EAEA;EACA,IAAIG,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,EAAEY,CAAC,EAAE,EAAE;IAC7B,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACV,MAAM,CAAC;IAC5DW,QAAQ,IAAID,KAAK,CAACG,WAAW,CAAC;EAClC;;EAEA;EACA,MAAMI,QAAQ,GAAGf,YAAY,GAAG,OAAO,CAACgB,IAAI,CAACP,QAAQ,CAAC,GAAG,IAAI;EAC7D,MAAMQ,QAAQ,GAAGhB,YAAY,GAAG,OAAO,CAACe,IAAI,CAACP,QAAQ,CAAC,GAAG,IAAI;EAC7D,MAAMS,UAAU,GAAGhB,cAAc,GAAG,IAAI,CAACc,IAAI,CAACP,QAAQ,CAAC,GAAG,IAAI;EAC9D,MAAMU,UAAU,GAAGhB,cAAc,GAAG,cAAc,CAACa,IAAI,CAACP,QAAQ,CAAC,GAAG,IAAI;;EAExE;EACA,IAAI,CAACM,QAAQ,IAAI,CAACE,QAAQ,IAAI,CAACC,UAAU,IAAI,CAACC,UAAU,EAAE;IACtD,OAAOtB,gBAAgB,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC5C;EAEA,OAAOU,QAAQ;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,yBAAyBA,CAACX,QAAQ,EAAE;EAChD,IAAI,CAACA,QAAQ,EAAE,OAAO,CAAC;EAEvB,IAAIY,KAAK,GAAG,CAAC;;EAEb;EACA,IAAIZ,QAAQ,CAACX,MAAM,IAAI,CAAC,EAAEuB,KAAK,IAAI,CAAC;EACpC,IAAIZ,QAAQ,CAACX,MAAM,IAAI,EAAE,EAAEuB,KAAK,IAAI,CAAC;;EAErC;EACA,IAAI,OAAO,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAEY,KAAK,IAAI,GAAG;EACxC,IAAI,OAAO,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAEY,KAAK,IAAI,GAAG;EACxC,IAAI,IAAI,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAEY,KAAK,IAAI,GAAG;EACrC,IAAI,cAAc,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAEY,KAAK,IAAI,GAAG;;EAE/C;EACA,MAAMC,OAAO,GAAG,CAAC,OAAO,CAACN,IAAI,CAACP,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KACxC,OAAO,CAACO,IAAI,CAACP,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAC/B,IAAI,CAACO,IAAI,CAACP,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAC5B,cAAc,CAACO,IAAI,CAACP,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAE7C,IAAIa,OAAO,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;;EAE5B;EACA,OAAOT,IAAI,CAACW,GAAG,CAAC,CAAC,EAAEX,IAAI,CAACC,KAAK,CAACQ,KAAK,CAAC,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,6BAA6BA,CAACf,QAAQ,EAAEgB,MAAM,EAAE;EAC5D,MAAMC,MAAM,GAAG,EAAE;;EAEjB;EACA,IAAIjB,QAAQ,CAACX,MAAM,GAAG2B,MAAM,CAACE,SAAS,EAAE;IACpCD,MAAM,CAACE,IAAI,CAAC,aAAaH,MAAM,CAACE,SAAS,IAAI,CAAC;EAClD;;EAEA;EACA,IAAIF,MAAM,CAACI,gBAAgB,IAAI,CAAC,OAAO,CAACb,IAAI,CAACP,QAAQ,CAAC,EAAE;IACpDiB,MAAM,CAACE,IAAI,CAAC,gBAAgB,CAAC;EACjC;;EAEA;EACA,IAAIH,MAAM,CAACK,gBAAgB,IAAI,CAAC,OAAO,CAACd,IAAI,CAACP,QAAQ,CAAC,EAAE;IACpDiB,MAAM,CAACE,IAAI,CAAC,gBAAgB,CAAC;EACjC;;EAEA;EACA,IAAIH,MAAM,CAACM,cAAc,IAAI,CAAC,IAAI,CAACf,IAAI,CAACP,QAAQ,CAAC,EAAE;IAC/CiB,MAAM,CAACE,IAAI,CAAC,cAAc,CAAC;EAC/B;;EAEA;EACA,IAAIH,MAAM,CAACO,cAAc,IAAI,CAAC,cAAc,CAAChB,IAAI,CAACP,QAAQ,CAAC,EAAE;IACzDiB,MAAM,CAACE,IAAI,CAAC,gBAAgB,CAAC;EACjC;;EAEA;EACA,IAAIH,MAAM,CAACQ,cAAc,IAAIR,MAAM,CAACS,QAAQ,EAAE;IAC1C,MAAMA,QAAQ,GAAGT,MAAM,CAACS,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC9C,IAAI1B,QAAQ,CAAC0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAAC,EAAE;MAC3CR,MAAM,CAACE,IAAI,CAAC,WAAW,CAAC;IAC5B;EACJ;EAEA,OAAO;IACHS,KAAK,EAAEX,MAAM,CAAC5B,MAAM,KAAK,CAAC;IAC1B4B;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,uBAAuBA,CAACC,QAAQ,EAAE;EAC9C,QAAQA,QAAQ;IACZ,KAAK,CAAC;MACF,OAAO;QAAEC,IAAI,EAAE,OAAO;QAAEC,UAAU,EAAE;MAAgB,CAAC;IACzD,KAAK,CAAC;MACF,OAAO;QAAED,IAAI,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAe,CAAC;IACpD,KAAK,CAAC;MACF,OAAO;QAAED,IAAI,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAkB,CAAC;IACxD,KAAK,CAAC;MACF,OAAO;QAAED,IAAI,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAiB,CAAC;IACtD,KAAK,CAAC;MACF,OAAO;QAAED,IAAI,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAiB,CAAC;IACxD;MACI,OAAO;QAAED,IAAI,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAG,CAAC;EAC3C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,cAAc,EAAEC,UAAU,EAAE;EAChE,IAAI,CAACD,cAAc,IAAI,CAACC,UAAU,EAAE;IAChC,OAAO;MAAEC,MAAM,EAAE,SAAS;MAAEC,aAAa,EAAE,CAAC;MAAEN,IAAI,EAAE;IAAK,CAAC;EAC9D;EAEA,MAAMO,UAAU,GAAG,IAAIC,IAAI,CAACL,cAAc,CAAC;EAC3C,MAAMM,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;EACtB,MAAME,UAAU,GAAG,IAAIF,IAAI,CAACD,UAAU,CAAC;EACvCG,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAGR,UAAU,CAAC;EAErD,MAAMS,QAAQ,GAAGH,UAAU,GAAGD,GAAG;EACjC,MAAMK,QAAQ,GAAG1C,IAAI,CAAC2C,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5D,IAAIR,MAAM,GAAG,QAAQ;EACrB,IAAIL,IAAI,GAAG,EAAE;EAEb,IAAIc,QAAQ,GAAG,CAAC,EAAE;IACdT,MAAM,GAAG,SAAS;IAClBL,IAAI,GAAG,MAAM5B,IAAI,CAAC4C,GAAG,CAACF,QAAQ,CAAC,GAAG;EACtC,CAAC,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;IACvBT,MAAM,GAAG,UAAU;IACnBL,IAAI,GAAG,MAAM;EACjB,CAAC,MAAM,IAAIc,QAAQ,IAAI,CAAC,EAAE;IACtBT,MAAM,GAAG,UAAU;IACnBL,IAAI,GAAG,GAAGc,QAAQ,MAAM;EAC5B,CAAC,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;IACtBT,MAAM,GAAG,SAAS;IAClBL,IAAI,GAAG,GAAGc,QAAQ,MAAM;EAC5B,CAAC,MAAM,IAAIA,QAAQ,IAAI,EAAE,EAAE;IACvBT,MAAM,GAAG,QAAQ;IACjBL,IAAI,GAAG,GAAGc,QAAQ,MAAM;EAC5B,CAAC,MAAM;IACHT,MAAM,GAAG,QAAQ;IACjBL,IAAI,GAAG,GAAGc,QAAQ,MAAM;EAC5B;EAEA,OAAO;IAAET,MAAM;IAAEC,aAAa,EAAEQ,QAAQ;IAAEd,IAAI;IAAEU;EAAW,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,2BAA2BA,CAAChD,QAAQ,EAAEgB,MAAM,GAAG,CAAC,CAAC,EAAE;EAC/D,MAAMiC,WAAW,GAAG,EAAE;EAEtB,IAAI,CAACjD,QAAQ,EAAE;IACXiD,WAAW,CAAC9B,IAAI,CAAC,OAAO,CAAC;IACzB,OAAO8B,WAAW;EACtB;;EAEA;EACA,IAAIjD,QAAQ,CAACX,MAAM,IAAI2B,MAAM,CAACE,SAAS,IAAI,CAAC,CAAC,EAAE;IAC3C+B,WAAW,CAAC9B,IAAI,CAAC,WAAWH,MAAM,CAACE,SAAS,IAAI,CAAC,GAAG,CAAC;EACzD,CAAC,MAAM,IAAIlB,QAAQ,CAACX,MAAM,GAAG,EAAE,EAAE;IAC7B4D,WAAW,CAAC9B,IAAI,CAAC,oBAAoB,CAAC;EAC1C;;EAEA;EACA,IAAIH,MAAM,CAACI,gBAAgB,IAAI,CAAC,OAAO,CAACb,IAAI,CAACP,QAAQ,CAAC,EAAE;IACpDiD,WAAW,CAAC9B,IAAI,CAAC,QAAQ,CAAC;EAC9B;EACA,IAAIH,MAAM,CAACK,gBAAgB,IAAI,CAAC,OAAO,CAACd,IAAI,CAACP,QAAQ,CAAC,EAAE;IACpDiD,WAAW,CAAC9B,IAAI,CAAC,QAAQ,CAAC;EAC9B;EACA,IAAIH,MAAM,CAACM,cAAc,IAAI,CAAC,IAAI,CAACf,IAAI,CAACP,QAAQ,CAAC,EAAE;IAC/CiD,WAAW,CAAC9B,IAAI,CAAC,MAAM,CAAC;EAC5B;EACA,IAAIH,MAAM,CAACO,cAAc,IAAI,CAAC,cAAc,CAAChB,IAAI,CAACP,QAAQ,CAAC,EAAE;IACzDiD,WAAW,CAAC9B,IAAI,CAAC,QAAQ,CAAC;EAC9B;;EAEA;EACA,IAAI,WAAW,CAACZ,IAAI,CAACP,QAAQ,CAAC,EAAE;IAC5BiD,WAAW,CAAC9B,IAAI,CAAC,WAAW,CAAC;EACjC;EACA,IAAI,cAAc,CAACZ,IAAI,CAACP,QAAQ,CAAC,EAAE;IAC/BiD,WAAW,CAAC9B,IAAI,CAAC,aAAa,CAAC;EACnC;;EAEA;EACA,MAAM+B,eAAe,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;EACzE,IAAIA,eAAe,CAACC,IAAI,CAACC,MAAM,IAAIpD,QAAQ,CAAC0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,MAAM,CAAC,CAAC,EAAE;IACzEH,WAAW,CAAC9B,IAAI,CAAC,aAAa,CAAC;EACnC;EAEA,IAAI8B,WAAW,CAAC5D,MAAM,KAAK,CAAC,EAAE;IAC1B4D,WAAW,CAAC9B,IAAI,CAAC,QAAQ,CAAC;EAC9B;EAEA,OAAO8B,WAAW;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,qBAAqBA,CAACrD,QAAQ,EAAEsD,MAAM,GAAG,IAAI,EAAEC,YAAY,GAAG,CAAC,EAAE;EAC7E,IAAI,CAACvD,QAAQ,EAAE,OAAO,EAAE;EAExB,IAAI,CAACsD,MAAM,EAAE;IACT,OAAOtD,QAAQ;EACnB;EAEA,IAAIuD,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAGvD,QAAQ,CAACX,MAAM,EAAE;IACpD,MAAMmE,OAAO,GAAGxD,QAAQ,CAACyD,SAAS,CAAC,CAAC,EAAEF,YAAY,CAAC;IACnD,MAAMG,MAAM,GAAG,GAAG,CAACC,MAAM,CAAC3D,QAAQ,CAACX,MAAM,GAAGkE,YAAY,CAAC;IACzD,OAAOC,OAAO,GAAGE,MAAM;EAC3B;EAEA,OAAO,GAAG,CAACC,MAAM,CAAC3D,QAAQ,CAACX,MAAM,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuE,cAAcA,CAAC5D,QAAQ,EAAE;EACrC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAM6D,YAAY,GAAG,CACjB,QAAQ;EAAY;EACpB,SAAS;EAAW;EACpB,UAAU;EAAU;EACpB,YAAY;EAAQ;EACpB,SAAS;EAAW;EACpB,QAAQ;EAAY;EACpB,UAAU;EAAU;EACpB,QAAQ;EAAY;EACpB,SAAS,CAAW;EAAA,CACvB;EAED,OAAOA,YAAY,CAACV,IAAI,CAACW,OAAO,IAAIA,OAAO,CAACvD,IAAI,CAACP,QAAQ,CAAC,CAAC,IAAIA,QAAQ,CAACX,MAAM,GAAG,CAAC;AACtF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0E,qBAAqBA,CAACC,WAAW,EAAEC,OAAO,GAAG,EAAE,EAAEC,UAAU,GAAG,CAAC,EAAE;EAC7E,MAAMC,SAAS,GAAG;IACdnE,QAAQ,EAAEgE,WAAW;IACrBI,SAAS,EAAE,IAAI7B,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC;IACnCC,EAAE,EAAE/B,IAAI,CAACC,GAAG,CAAC;EACjB,CAAC;EAED,MAAM+B,cAAc,GAAG,CAACJ,SAAS,EAAE,GAAGF,OAAO,CAAC;EAC9C,OAAOM,cAAc,CAACC,KAAK,CAAC,CAAC,EAAEN,UAAU,CAAC;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,mBAAmBA,CAACzE,QAAQ,EAAEiE,OAAO,GAAG,EAAE,EAAE;EACxD,OAAOA,OAAO,CAACd,IAAI,CAACuB,MAAM,IAAIA,MAAM,CAAC1E,QAAQ,KAAKA,QAAQ,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2E,gCAAgCA,CAAC3E,QAAQ,EAAE;EACvD,IAAI,CAACA,QAAQ,EAAE;IACX,OAAO;MACHY,KAAK,EAAE,CAAC;MACRgE,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE;QACLxF,MAAM,EAAE;UAAEuB,KAAK,EAAE,CAAC;UAAEkE,GAAG,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAK,CAAC;QAChDlE,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAEkE,GAAG,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAQ,CAAC;QACpDC,UAAU,EAAE;UAAEpE,KAAK,EAAE,CAAC;UAAEkE,GAAG,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAM,CAAC;QACrDE,QAAQ,EAAE;UAAErE,KAAK,EAAE,CAAC;UAAEkE,GAAG,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAQ;MACxD;IACJ,CAAC;EACL;EAEA,MAAMF,OAAO,GAAG;IACZxF,MAAM,EAAE;MAAEuB,KAAK,EAAE,CAAC;MAAEkE,GAAG,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAK,CAAC;IAChDlE,OAAO,EAAE;MAAED,KAAK,EAAE,CAAC;MAAEkE,GAAG,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAQ,CAAC;IACpDC,UAAU,EAAE;MAAEpE,KAAK,EAAE,CAAC;MAAEkE,GAAG,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAM,CAAC;IACrDE,QAAQ,EAAE;MAAErE,KAAK,EAAE,CAAC;MAAEkE,GAAG,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAQ;EACxD,CAAC;;EAED;EACA,IAAI/E,QAAQ,CAACX,MAAM,IAAI,CAAC,EAAEwF,OAAO,CAACxF,MAAM,CAACuB,KAAK,IAAI,EAAE;EACpD,IAAIZ,QAAQ,CAACX,MAAM,IAAI,EAAE,EAAEwF,OAAO,CAACxF,MAAM,CAACuB,KAAK,IAAI,EAAE;EACrD,IAAIZ,QAAQ,CAACX,MAAM,IAAI,EAAE,EAAEwF,OAAO,CAACxF,MAAM,CAACuB,KAAK,IAAI,CAAC;;EAEpD;EACA,IAAI,OAAO,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAE6E,OAAO,CAAChE,OAAO,CAACD,KAAK,IAAI,CAAC;EACtD,IAAI,OAAO,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAE6E,OAAO,CAAChE,OAAO,CAACD,KAAK,IAAI,CAAC;EACtD,IAAI,IAAI,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAE6E,OAAO,CAAChE,OAAO,CAACD,KAAK,IAAI,CAAC;EACnD,IAAI,cAAc,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAE6E,OAAO,CAAChE,OAAO,CAACD,KAAK,IAAI,CAAC;;EAE7D;EACA,IAAI,CAACgD,cAAc,CAAC5D,QAAQ,CAAC,EAAE6E,OAAO,CAACG,UAAU,CAACpE,KAAK,IAAI,EAAE;;EAE7D;EACA,IAAI,CAAC,WAAW,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAE6E,OAAO,CAACI,QAAQ,CAACrE,KAAK,IAAI,CAAC,EAAE;EAC9D,IAAI,CAAC,cAAc,CAACL,IAAI,CAACP,QAAQ,CAAC,EAAE6E,OAAO,CAACI,QAAQ,CAACrE,KAAK,IAAI,CAAC,EAAE;EACjE,IAAIZ,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAEwF,OAAO,CAACI,QAAQ,CAACrE,KAAK,IAAIT,IAAI,CAACW,GAAG,CAAC,CAAC,EAAEd,QAAQ,CAACX,MAAM,CAAC,EAAE;;EAEjF,MAAM6F,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACP,OAAO,CAAC,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAAC3E,KAAK,EAAE,CAAC,CAAC;EAEpF,OAAO;IACHA,KAAK,EAAEsE,UAAU;IACjBN,QAAQ,EAAE,GAAG;IACbC;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}