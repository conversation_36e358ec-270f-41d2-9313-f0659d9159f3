{"ast": null, "code": "/**\n * 数据验证工具类\n * 提供各种数据验证功能\n */\n\n/**\n * 验证规则类型\n */\nexport const ValidationRules = {\n  REQUIRED: 'required',\n  MIN_LENGTH: 'minLength',\n  MAX_LENGTH: 'maxLength',\n  PATTERN: 'pattern',\n  EMAIL: 'email',\n  IP: 'ip',\n  URL: 'url',\n  NUMBER: 'number',\n  INTEGER: 'integer',\n  POSITIVE: 'positive',\n  RANGE: 'range',\n  CUSTOM: 'custom'\n};\n\n/**\n * 验证器类\n */\nexport class Validator {\n  constructor() {\n    this.rules = new Map();\n    this.setupDefaultRules();\n  }\n\n  /**\n   * 设置默认验证规则\n   */\n  setupDefaultRules() {\n    // 必填验证\n    this.addRule(ValidationRules.REQUIRED, value => {\n      if (value === null || value === undefined || value === '') {\n        return '此字段为必填项';\n      }\n      return null;\n    });\n\n    // 最小长度验证\n    this.addRule(ValidationRules.MIN_LENGTH, (value, minLength) => {\n      if (value && value.length < minLength) {\n        return `最少需要${minLength}个字符`;\n      }\n      return null;\n    });\n\n    // 最大长度验证\n    this.addRule(ValidationRules.MAX_LENGTH, (value, maxLength) => {\n      if (value && value.length > maxLength) {\n        return `最多允许${maxLength}个字符`;\n      }\n      return null;\n    });\n\n    // 正则表达式验证\n    this.addRule(ValidationRules.PATTERN, (value, pattern, message) => {\n      if (value && !pattern.test(value)) {\n        return message || '格式不正确';\n      }\n      return null;\n    });\n\n    // 邮箱验证\n    this.addRule(ValidationRules.EMAIL, value => {\n      const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (value && !emailPattern.test(value)) {\n        return '请输入有效的邮箱地址';\n      }\n      return null;\n    });\n\n    // IP地址验证\n    this.addRule(ValidationRules.IP, value => {\n      const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n      if (value && !ipPattern.test(value)) {\n        return '请输入有效的IP地址';\n      }\n      return null;\n    });\n\n    // URL验证\n    this.addRule(ValidationRules.URL, value => {\n      try {\n        if (value) {\n          new URL(value);\n        }\n        return null;\n      } catch {\n        return '请输入有效的URL地址';\n      }\n    });\n\n    // 数字验证\n    this.addRule(ValidationRules.NUMBER, value => {\n      if (value && isNaN(Number(value))) {\n        return '请输入有效的数字';\n      }\n      return null;\n    });\n\n    // 整数验证\n    this.addRule(ValidationRules.INTEGER, value => {\n      if (value && !Number.isInteger(Number(value))) {\n        return '请输入有效的整数';\n      }\n      return null;\n    });\n\n    // 正数验证\n    this.addRule(ValidationRules.POSITIVE, value => {\n      if (value && Number(value) <= 0) {\n        return '请输入正数';\n      }\n      return null;\n    });\n\n    // 范围验证\n    this.addRule(ValidationRules.RANGE, (value, min, max) => {\n      const num = Number(value);\n      if (value && (num < min || num > max)) {\n        return `请输入${min}到${max}之间的数值`;\n      }\n      return null;\n    });\n  }\n\n  /**\n   * 添加验证规则\n   * @param {string} name 规则名称\n   * @param {Function} validator 验证函数\n   */\n  addRule(name, validator) {\n    this.rules.set(name, validator);\n  }\n\n  /**\n   * 验证单个值\n   * @param {any} value 要验证的值\n   * @param {Array} rules 验证规则数组\n   * @returns {Array} 错误消息数组\n   */\n  validate(value, rules = []) {\n    const errors = [];\n    for (const rule of rules) {\n      let ruleName, ruleParams, customMessage;\n      if (typeof rule === 'string') {\n        ruleName = rule;\n        ruleParams = [];\n      } else if (Array.isArray(rule)) {\n        [ruleName, ...ruleParams] = rule;\n      } else if (typeof rule === 'object') {\n        ruleName = rule.rule;\n        ruleParams = rule.params || [];\n        customMessage = rule.message;\n      }\n      const validator = this.rules.get(ruleName);\n      if (!validator) {\n        console.warn(`Unknown validation rule: ${ruleName}`);\n        continue;\n      }\n      const error = validator(value, ...ruleParams);\n      if (error) {\n        errors.push(customMessage || error);\n      }\n    }\n    return errors;\n  }\n\n  /**\n   * 验证对象\n   * @param {Object} data 要验证的数据对象\n   * @param {Object} schema 验证模式\n   * @returns {Object} 验证结果\n   */\n  validateObject(data, schema) {\n    const errors = {};\n    let isValid = true;\n    for (const [field, rules] of Object.entries(schema)) {\n      const fieldErrors = this.validate(data[field], rules);\n      if (fieldErrors.length > 0) {\n        errors[field] = fieldErrors;\n        isValid = false;\n      }\n    }\n    return {\n      isValid,\n      errors,\n      data\n    };\n  }\n}\n\n// 创建默认验证器实例\nexport const validator = new Validator();\n\n/**\n * 便捷的验证函数\n */\nexport function validateField(value, rules) {\n  return validator.validate(value, rules);\n}\nexport function validateForm(data, schema) {\n  return validator.validateObject(data, schema);\n}\n\n/**\n * 常用验证模式\n */\nexport const ValidationSchemas = {\n  // 主机信息验证\n  host: {\n    name: [ValidationRules.REQUIRED, [ValidationRules.MIN_LENGTH, 1], [ValidationRules.MAX_LENGTH, 50], [ValidationRules.PATTERN, /^[a-zA-Z0-9\\-_.]+$/, '主机名只能包含字母、数字、连字符、下划线和点']],\n    ip: [ValidationRules.REQUIRED, ValidationRules.IP]\n  },\n  // 账号信息验证\n  account: {\n    username: [ValidationRules.REQUIRED, [ValidationRules.MIN_LENGTH, 2], [ValidationRules.MAX_LENGTH, 32], [ValidationRules.PATTERN, /^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线']],\n    password: [ValidationRules.REQUIRED, [ValidationRules.MIN_LENGTH, 8]]\n  },\n  // 密码策略验证\n  policy: {\n    name: [ValidationRules.REQUIRED, [ValidationRules.MIN_LENGTH, 2], [ValidationRules.MAX_LENGTH, 50]],\n    minLength: [ValidationRules.REQUIRED, ValidationRules.INTEGER, ValidationRules.POSITIVE, [ValidationRules.RANGE, 6, 128]],\n    expiryDays: [ValidationRules.REQUIRED, ValidationRules.INTEGER, ValidationRules.POSITIVE, [ValidationRules.RANGE, 1, 365]],\n    historyCount: [ValidationRules.INTEGER, ValidationRules.POSITIVE, [ValidationRules.RANGE, 1, 50]]\n  },\n  // 定时任务验证\n  task: {\n    name: [ValidationRules.REQUIRED, [ValidationRules.MIN_LENGTH, 2], [ValidationRules.MAX_LENGTH, 100]],\n    target: [ValidationRules.REQUIRED, [ValidationRules.MIN_LENGTH, 2]],\n    schedule: [ValidationRules.REQUIRED]\n  }\n};\n\n/**\n * 特殊验证函数\n */\n\n/**\n * 验证密码强度\n * @param {string} password 密码\n * @param {Object} policy 密码策略\n * @returns {Array} 错误消息数组\n */\nexport function validatePasswordStrength(password, policy = {}) {\n  const errors = [];\n  if (!password) {\n    errors.push('密码不能为空');\n    return errors;\n  }\n\n  // 长度检查\n  if (policy.minLength && password.length < policy.minLength) {\n    errors.push(`密码长度不能少于${policy.minLength}位`);\n  }\n\n  // 字符要求检查\n  if (policy.requireUppercase && !/[A-Z]/.test(password)) {\n    errors.push('密码必须包含大写字母');\n  }\n  if (policy.requireLowercase && !/[a-z]/.test(password)) {\n    errors.push('密码必须包含小写字母');\n  }\n  if (policy.requireNumbers && !/[0-9]/.test(password)) {\n    errors.push('密码必须包含数字');\n  }\n  if (policy.requireSpecial && !/[^a-zA-Z0-9]/.test(password)) {\n    errors.push('密码必须包含特殊字符');\n  }\n  return errors;\n}\n\n/**\n * 验证Cron表达式\n * @param {string} cronExpression Cron表达式\n * @returns {Array} 错误消息数组\n */\nexport function validateCronExpression(cronExpression) {\n  const errors = [];\n  if (!cronExpression) {\n    errors.push('Cron表达式不能为空');\n    return errors;\n  }\n\n  // 简单的Cron表达式验证\n  const parts = cronExpression.trim().split(/\\s+/);\n  if (parts.length !== 5 && parts.length !== 6) {\n    errors.push('Cron表达式格式不正确');\n  }\n  return errors;\n}\n\n/**\n * 验证JSON格式\n * @param {string} jsonString JSON字符串\n * @returns {Array} 错误消息数组\n */\nexport function validateJSON(jsonString) {\n  const errors = [];\n  if (!jsonString) {\n    errors.push('JSON不能为空');\n    return errors;\n  }\n  try {\n    JSON.parse(jsonString);\n  } catch (e) {\n    errors.push('JSON格式不正确');\n  }\n  return errors;\n}\nexport default validator;", "map": {"version": 3, "names": ["ValidationRules", "REQUIRED", "MIN_LENGTH", "MAX_LENGTH", "PATTERN", "EMAIL", "IP", "URL", "NUMBER", "INTEGER", "POSITIVE", "RANGE", "CUSTOM", "Validator", "constructor", "rules", "Map", "setupDefaultRules", "addRule", "value", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "pattern", "message", "test", "emailPattern", "ipPattern", "isNaN", "Number", "isInteger", "min", "max", "num", "name", "validator", "set", "validate", "errors", "rule", "ruleName", "ruleParams", "customMessage", "Array", "isArray", "params", "get", "console", "warn", "error", "push", "validateObject", "data", "schema", "<PERSON><PERSON><PERSON><PERSON>", "field", "Object", "entries", "fieldErrors", "validateField", "validateForm", "ValidationSchemas", "host", "ip", "account", "username", "password", "policy", "expiryDays", "historyCount", "task", "target", "schedule", "validatePasswordStrength", "requireUppercase", "requireLowercase", "requireNumbers", "requireSpecial", "validateCronExpression", "cronExpression", "parts", "trim", "split", "validateJSON", "jsonString", "JSON", "parse", "e"], "sources": ["D:/demo/ooo/pass/src/utils/validation.js"], "sourcesContent": ["/**\n * 数据验证工具类\n * 提供各种数据验证功能\n */\n\n/**\n * 验证规则类型\n */\nexport const ValidationRules = {\n  REQUIRED: 'required',\n  MIN_LENGTH: 'minLength',\n  MAX_LENGTH: 'maxLength',\n  PATTERN: 'pattern',\n  EMAIL: 'email',\n  IP: 'ip',\n  URL: 'url',\n  NUMBER: 'number',\n  INTEGER: 'integer',\n  POSITIVE: 'positive',\n  RANGE: 'range',\n  CUSTOM: 'custom'\n}\n\n/**\n * 验证器类\n */\nexport class Validator {\n  constructor() {\n    this.rules = new Map()\n    this.setupDefaultRules()\n  }\n\n  /**\n   * 设置默认验证规则\n   */\n  setupDefaultRules() {\n    // 必填验证\n    this.addRule(ValidationRules.REQUIRED, (value) => {\n      if (value === null || value === undefined || value === '') {\n        return '此字段为必填项'\n      }\n      return null\n    })\n\n    // 最小长度验证\n    this.addRule(ValidationRules.MIN_LENGTH, (value, minLength) => {\n      if (value && value.length < minLength) {\n        return `最少需要${minLength}个字符`\n      }\n      return null\n    })\n\n    // 最大长度验证\n    this.addRule(ValidationRules.MAX_LENGTH, (value, maxLength) => {\n      if (value && value.length > maxLength) {\n        return `最多允许${maxLength}个字符`\n      }\n      return null\n    })\n\n    // 正则表达式验证\n    this.addRule(ValidationRules.PATTERN, (value, pattern, message) => {\n      if (value && !pattern.test(value)) {\n        return message || '格式不正确'\n      }\n      return null\n    })\n\n    // 邮箱验证\n    this.addRule(ValidationRules.EMAIL, (value) => {\n      const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n      if (value && !emailPattern.test(value)) {\n        return '请输入有效的邮箱地址'\n      }\n      return null\n    })\n\n    // IP地址验证\n    this.addRule(ValidationRules.IP, (value) => {\n      const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n      if (value && !ipPattern.test(value)) {\n        return '请输入有效的IP地址'\n      }\n      return null\n    })\n\n    // URL验证\n    this.addRule(ValidationRules.URL, (value) => {\n      try {\n        if (value) {\n          new URL(value)\n        }\n        return null\n      } catch {\n        return '请输入有效的URL地址'\n      }\n    })\n\n    // 数字验证\n    this.addRule(ValidationRules.NUMBER, (value) => {\n      if (value && isNaN(Number(value))) {\n        return '请输入有效的数字'\n      }\n      return null\n    })\n\n    // 整数验证\n    this.addRule(ValidationRules.INTEGER, (value) => {\n      if (value && (!Number.isInteger(Number(value)))) {\n        return '请输入有效的整数'\n      }\n      return null\n    })\n\n    // 正数验证\n    this.addRule(ValidationRules.POSITIVE, (value) => {\n      if (value && Number(value) <= 0) {\n        return '请输入正数'\n      }\n      return null\n    })\n\n    // 范围验证\n    this.addRule(ValidationRules.RANGE, (value, min, max) => {\n      const num = Number(value)\n      if (value && (num < min || num > max)) {\n        return `请输入${min}到${max}之间的数值`\n      }\n      return null\n    })\n  }\n\n  /**\n   * 添加验证规则\n   * @param {string} name 规则名称\n   * @param {Function} validator 验证函数\n   */\n  addRule(name, validator) {\n    this.rules.set(name, validator)\n  }\n\n  /**\n   * 验证单个值\n   * @param {any} value 要验证的值\n   * @param {Array} rules 验证规则数组\n   * @returns {Array} 错误消息数组\n   */\n  validate(value, rules = []) {\n    const errors = []\n\n    for (const rule of rules) {\n      let ruleName, ruleParams, customMessage\n\n      if (typeof rule === 'string') {\n        ruleName = rule\n        ruleParams = []\n      } else if (Array.isArray(rule)) {\n        [ruleName, ...ruleParams] = rule\n      } else if (typeof rule === 'object') {\n        ruleName = rule.rule\n        ruleParams = rule.params || []\n        customMessage = rule.message\n      }\n\n      const validator = this.rules.get(ruleName)\n      if (!validator) {\n        console.warn(`Unknown validation rule: ${ruleName}`)\n        continue\n      }\n\n      const error = validator(value, ...ruleParams)\n      if (error) {\n        errors.push(customMessage || error)\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * 验证对象\n   * @param {Object} data 要验证的数据对象\n   * @param {Object} schema 验证模式\n   * @returns {Object} 验证结果\n   */\n  validateObject(data, schema) {\n    const errors = {}\n    let isValid = true\n\n    for (const [field, rules] of Object.entries(schema)) {\n      const fieldErrors = this.validate(data[field], rules)\n      if (fieldErrors.length > 0) {\n        errors[field] = fieldErrors\n        isValid = false\n      }\n    }\n\n    return {\n      isValid,\n      errors,\n      data\n    }\n  }\n}\n\n// 创建默认验证器实例\nexport const validator = new Validator()\n\n/**\n * 便捷的验证函数\n */\nexport function validateField(value, rules) {\n  return validator.validate(value, rules)\n}\n\nexport function validateForm(data, schema) {\n  return validator.validateObject(data, schema)\n}\n\n/**\n * 常用验证模式\n */\nexport const ValidationSchemas = {\n  // 主机信息验证\n  host: {\n    name: [\n      ValidationRules.REQUIRED,\n      [ValidationRules.MIN_LENGTH, 1],\n      [ValidationRules.MAX_LENGTH, 50],\n      [ValidationRules.PATTERN, /^[a-zA-Z0-9\\-_.]+$/, '主机名只能包含字母、数字、连字符、下划线和点']\n    ],\n    ip: [\n      ValidationRules.REQUIRED,\n      ValidationRules.IP\n    ]\n  },\n\n  // 账号信息验证\n  account: {\n    username: [\n      ValidationRules.REQUIRED,\n      [ValidationRules.MIN_LENGTH, 2],\n      [ValidationRules.MAX_LENGTH, 32],\n      [ValidationRules.PATTERN, /^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线']\n    ],\n    password: [\n      ValidationRules.REQUIRED,\n      [ValidationRules.MIN_LENGTH, 8]\n    ]\n  },\n\n  // 密码策略验证\n  policy: {\n    name: [\n      ValidationRules.REQUIRED,\n      [ValidationRules.MIN_LENGTH, 2],\n      [ValidationRules.MAX_LENGTH, 50]\n    ],\n    minLength: [\n      ValidationRules.REQUIRED,\n      ValidationRules.INTEGER,\n      ValidationRules.POSITIVE,\n      [ValidationRules.RANGE, 6, 128]\n    ],\n    expiryDays: [\n      ValidationRules.REQUIRED,\n      ValidationRules.INTEGER,\n      ValidationRules.POSITIVE,\n      [ValidationRules.RANGE, 1, 365]\n    ],\n    historyCount: [\n      ValidationRules.INTEGER,\n      ValidationRules.POSITIVE,\n      [ValidationRules.RANGE, 1, 50]\n    ]\n  },\n\n  // 定时任务验证\n  task: {\n    name: [\n      ValidationRules.REQUIRED,\n      [ValidationRules.MIN_LENGTH, 2],\n      [ValidationRules.MAX_LENGTH, 100]\n    ],\n    target: [\n      ValidationRules.REQUIRED,\n      [ValidationRules.MIN_LENGTH, 2]\n    ],\n    schedule: [\n      ValidationRules.REQUIRED\n    ]\n  }\n}\n\n/**\n * 特殊验证函数\n */\n\n/**\n * 验证密码强度\n * @param {string} password 密码\n * @param {Object} policy 密码策略\n * @returns {Array} 错误消息数组\n */\nexport function validatePasswordStrength(password, policy = {}) {\n  const errors = []\n\n  if (!password) {\n    errors.push('密码不能为空')\n    return errors\n  }\n\n  // 长度检查\n  if (policy.minLength && password.length < policy.minLength) {\n    errors.push(`密码长度不能少于${policy.minLength}位`)\n  }\n\n  // 字符要求检查\n  if (policy.requireUppercase && !/[A-Z]/.test(password)) {\n    errors.push('密码必须包含大写字母')\n  }\n\n  if (policy.requireLowercase && !/[a-z]/.test(password)) {\n    errors.push('密码必须包含小写字母')\n  }\n\n  if (policy.requireNumbers && !/[0-9]/.test(password)) {\n    errors.push('密码必须包含数字')\n  }\n\n  if (policy.requireSpecial && !/[^a-zA-Z0-9]/.test(password)) {\n    errors.push('密码必须包含特殊字符')\n  }\n\n  return errors\n}\n\n/**\n * 验证Cron表达式\n * @param {string} cronExpression Cron表达式\n * @returns {Array} 错误消息数组\n */\nexport function validateCronExpression(cronExpression) {\n  const errors = []\n\n  if (!cronExpression) {\n    errors.push('Cron表达式不能为空')\n    return errors\n  }\n\n  // 简单的Cron表达式验证\n  const parts = cronExpression.trim().split(/\\s+/)\n  if (parts.length !== 5 && parts.length !== 6) {\n    errors.push('Cron表达式格式不正确')\n  }\n\n  return errors\n}\n\n/**\n * 验证JSON格式\n * @param {string} jsonString JSON字符串\n * @returns {Array} 错误消息数组\n */\nexport function validateJSON(jsonString) {\n  const errors = []\n\n  if (!jsonString) {\n    errors.push('JSON不能为空')\n    return errors\n  }\n\n  try {\n    JSON.parse(jsonString)\n  } catch (e) {\n    errors.push('JSON格式不正确')\n  }\n\n  return errors\n}\n\nexport default validator\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,eAAe,GAAG;EAC7BC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,WAAW;EACvBC,UAAU,EAAE,WAAW;EACvBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEA,iBAAiBA,CAAA,EAAG;IAClB;IACA,IAAI,CAACC,OAAO,CAAClB,eAAe,CAACC,QAAQ,EAAGkB,KAAK,IAAK;MAChD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;QACzD,OAAO,SAAS;MAClB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACE,UAAU,EAAE,CAACiB,KAAK,EAAEE,SAAS,KAAK;MAC7D,IAAIF,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAGD,SAAS,EAAE;QACrC,OAAO,OAAOA,SAAS,KAAK;MAC9B;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACH,OAAO,CAAClB,eAAe,CAACG,UAAU,EAAE,CAACgB,KAAK,EAAEI,SAAS,KAAK;MAC7D,IAAIJ,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAGC,SAAS,EAAE;QACrC,OAAO,OAAOA,SAAS,KAAK;MAC9B;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACL,OAAO,CAAClB,eAAe,CAACI,OAAO,EAAE,CAACe,KAAK,EAAEK,OAAO,EAAEC,OAAO,KAAK;MACjE,IAAIN,KAAK,IAAI,CAACK,OAAO,CAACE,IAAI,CAACP,KAAK,CAAC,EAAE;QACjC,OAAOM,OAAO,IAAI,OAAO;MAC3B;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,OAAO,CAAClB,eAAe,CAACK,KAAK,EAAGc,KAAK,IAAK;MAC7C,MAAMQ,YAAY,GAAG,4BAA4B;MACjD,IAAIR,KAAK,IAAI,CAACQ,YAAY,CAACD,IAAI,CAACP,KAAK,CAAC,EAAE;QACtC,OAAO,YAAY;MACrB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACM,EAAE,EAAGa,KAAK,IAAK;MAC1C,MAAMS,SAAS,GAAG,6FAA6F;MAC/G,IAAIT,KAAK,IAAI,CAACS,SAAS,CAACF,IAAI,CAACP,KAAK,CAAC,EAAE;QACnC,OAAO,YAAY;MACrB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACO,GAAG,EAAGY,KAAK,IAAK;MAC3C,IAAI;QACF,IAAIA,KAAK,EAAE;UACT,IAAIZ,GAAG,CAACY,KAAK,CAAC;QAChB;QACA,OAAO,IAAI;MACb,CAAC,CAAC,MAAM;QACN,OAAO,aAAa;MACtB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACQ,MAAM,EAAGW,KAAK,IAAK;MAC9C,IAAIA,KAAK,IAAIU,KAAK,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC,EAAE;QACjC,OAAO,UAAU;MACnB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACS,OAAO,EAAGU,KAAK,IAAK;MAC/C,IAAIA,KAAK,IAAK,CAACW,MAAM,CAACC,SAAS,CAACD,MAAM,CAACX,KAAK,CAAC,CAAE,EAAE;QAC/C,OAAO,UAAU;MACnB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACU,QAAQ,EAAGS,KAAK,IAAK;MAChD,IAAIA,KAAK,IAAIW,MAAM,CAACX,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/B,OAAO,OAAO;MAChB;MACA,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,OAAO,CAAClB,eAAe,CAACW,KAAK,EAAE,CAACQ,KAAK,EAAEa,GAAG,EAAEC,GAAG,KAAK;MACvD,MAAMC,GAAG,GAAGJ,MAAM,CAACX,KAAK,CAAC;MACzB,IAAIA,KAAK,KAAKe,GAAG,GAAGF,GAAG,IAAIE,GAAG,GAAGD,GAAG,CAAC,EAAE;QACrC,OAAO,MAAMD,GAAG,IAAIC,GAAG,OAAO;MAChC;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEf,OAAOA,CAACiB,IAAI,EAAEC,SAAS,EAAE;IACvB,IAAI,CAACrB,KAAK,CAACsB,GAAG,CAACF,IAAI,EAAEC,SAAS,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEE,QAAQA,CAACnB,KAAK,EAAEJ,KAAK,GAAG,EAAE,EAAE;IAC1B,MAAMwB,MAAM,GAAG,EAAE;IAEjB,KAAK,MAAMC,IAAI,IAAIzB,KAAK,EAAE;MACxB,IAAI0B,QAAQ,EAAEC,UAAU,EAAEC,aAAa;MAEvC,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;QAC5BC,QAAQ,GAAGD,IAAI;QACfE,UAAU,GAAG,EAAE;MACjB,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;QAC9B,CAACC,QAAQ,EAAE,GAAGC,UAAU,CAAC,GAAGF,IAAI;MAClC,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACnCC,QAAQ,GAAGD,IAAI,CAACA,IAAI;QACpBE,UAAU,GAAGF,IAAI,CAACM,MAAM,IAAI,EAAE;QAC9BH,aAAa,GAAGH,IAAI,CAACf,OAAO;MAC9B;MAEA,MAAMW,SAAS,GAAG,IAAI,CAACrB,KAAK,CAACgC,GAAG,CAACN,QAAQ,CAAC;MAC1C,IAAI,CAACL,SAAS,EAAE;QACdY,OAAO,CAACC,IAAI,CAAC,4BAA4BR,QAAQ,EAAE,CAAC;QACpD;MACF;MAEA,MAAMS,KAAK,GAAGd,SAAS,CAACjB,KAAK,EAAE,GAAGuB,UAAU,CAAC;MAC7C,IAAIQ,KAAK,EAAE;QACTX,MAAM,CAACY,IAAI,CAACR,aAAa,IAAIO,KAAK,CAAC;MACrC;IACF;IAEA,OAAOX,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEa,cAAcA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC3B,MAAMf,MAAM,GAAG,CAAC,CAAC;IACjB,IAAIgB,OAAO,GAAG,IAAI;IAElB,KAAK,MAAM,CAACC,KAAK,EAAEzC,KAAK,CAAC,IAAI0C,MAAM,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;MACnD,MAAMK,WAAW,GAAG,IAAI,CAACrB,QAAQ,CAACe,IAAI,CAACG,KAAK,CAAC,EAAEzC,KAAK,CAAC;MACrD,IAAI4C,WAAW,CAACrC,MAAM,GAAG,CAAC,EAAE;QAC1BiB,MAAM,CAACiB,KAAK,CAAC,GAAGG,WAAW;QAC3BJ,OAAO,GAAG,KAAK;MACjB;IACF;IAEA,OAAO;MACLA,OAAO;MACPhB,MAAM;MACNc;IACF,CAAC;EACH;AACF;;AAEA;AACA,OAAO,MAAMjB,SAAS,GAAG,IAAIvB,SAAS,CAAC,CAAC;;AAExC;AACA;AACA;AACA,OAAO,SAAS+C,aAAaA,CAACzC,KAAK,EAAEJ,KAAK,EAAE;EAC1C,OAAOqB,SAAS,CAACE,QAAQ,CAACnB,KAAK,EAAEJ,KAAK,CAAC;AACzC;AAEA,OAAO,SAAS8C,YAAYA,CAACR,IAAI,EAAEC,MAAM,EAAE;EACzC,OAAOlB,SAAS,CAACgB,cAAc,CAACC,IAAI,EAAEC,MAAM,CAAC;AAC/C;;AAEA;AACA;AACA;AACA,OAAO,MAAMQ,iBAAiB,GAAG;EAC/B;EACAC,IAAI,EAAE;IACJ5B,IAAI,EAAE,CACJnC,eAAe,CAACC,QAAQ,EACxB,CAACD,eAAe,CAACE,UAAU,EAAE,CAAC,CAAC,EAC/B,CAACF,eAAe,CAACG,UAAU,EAAE,EAAE,CAAC,EAChC,CAACH,eAAe,CAACI,OAAO,EAAE,oBAAoB,EAAE,wBAAwB,CAAC,CAC1E;IACD4D,EAAE,EAAE,CACFhE,eAAe,CAACC,QAAQ,EACxBD,eAAe,CAACM,EAAE;EAEtB,CAAC;EAED;EACA2D,OAAO,EAAE;IACPC,QAAQ,EAAE,CACRlE,eAAe,CAACC,QAAQ,EACxB,CAACD,eAAe,CAACE,UAAU,EAAE,CAAC,CAAC,EAC/B,CAACF,eAAe,CAACG,UAAU,EAAE,EAAE,CAAC,EAChC,CAACH,eAAe,CAACI,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CACjE;IACD+D,QAAQ,EAAE,CACRnE,eAAe,CAACC,QAAQ,EACxB,CAACD,eAAe,CAACE,UAAU,EAAE,CAAC,CAAC;EAEnC,CAAC;EAED;EACAkE,MAAM,EAAE;IACNjC,IAAI,EAAE,CACJnC,eAAe,CAACC,QAAQ,EACxB,CAACD,eAAe,CAACE,UAAU,EAAE,CAAC,CAAC,EAC/B,CAACF,eAAe,CAACG,UAAU,EAAE,EAAE,CAAC,CACjC;IACDkB,SAAS,EAAE,CACTrB,eAAe,CAACC,QAAQ,EACxBD,eAAe,CAACS,OAAO,EACvBT,eAAe,CAACU,QAAQ,EACxB,CAACV,eAAe,CAACW,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAChC;IACD0D,UAAU,EAAE,CACVrE,eAAe,CAACC,QAAQ,EACxBD,eAAe,CAACS,OAAO,EACvBT,eAAe,CAACU,QAAQ,EACxB,CAACV,eAAe,CAACW,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAChC;IACD2D,YAAY,EAAE,CACZtE,eAAe,CAACS,OAAO,EACvBT,eAAe,CAACU,QAAQ,EACxB,CAACV,eAAe,CAACW,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;EAElC,CAAC;EAED;EACA4D,IAAI,EAAE;IACJpC,IAAI,EAAE,CACJnC,eAAe,CAACC,QAAQ,EACxB,CAACD,eAAe,CAACE,UAAU,EAAE,CAAC,CAAC,EAC/B,CAACF,eAAe,CAACG,UAAU,EAAE,GAAG,CAAC,CAClC;IACDqE,MAAM,EAAE,CACNxE,eAAe,CAACC,QAAQ,EACxB,CAACD,eAAe,CAACE,UAAU,EAAE,CAAC,CAAC,CAChC;IACDuE,QAAQ,EAAE,CACRzE,eAAe,CAACC,QAAQ;EAE5B;AACF,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyE,wBAAwBA,CAACP,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;EAC9D,MAAM7B,MAAM,GAAG,EAAE;EAEjB,IAAI,CAAC4B,QAAQ,EAAE;IACb5B,MAAM,CAACY,IAAI,CAAC,QAAQ,CAAC;IACrB,OAAOZ,MAAM;EACf;;EAEA;EACA,IAAI6B,MAAM,CAAC/C,SAAS,IAAI8C,QAAQ,CAAC7C,MAAM,GAAG8C,MAAM,CAAC/C,SAAS,EAAE;IAC1DkB,MAAM,CAACY,IAAI,CAAC,WAAWiB,MAAM,CAAC/C,SAAS,GAAG,CAAC;EAC7C;;EAEA;EACA,IAAI+C,MAAM,CAACO,gBAAgB,IAAI,CAAC,OAAO,CAACjD,IAAI,CAACyC,QAAQ,CAAC,EAAE;IACtD5B,MAAM,CAACY,IAAI,CAAC,YAAY,CAAC;EAC3B;EAEA,IAAIiB,MAAM,CAACQ,gBAAgB,IAAI,CAAC,OAAO,CAAClD,IAAI,CAACyC,QAAQ,CAAC,EAAE;IACtD5B,MAAM,CAACY,IAAI,CAAC,YAAY,CAAC;EAC3B;EAEA,IAAIiB,MAAM,CAACS,cAAc,IAAI,CAAC,OAAO,CAACnD,IAAI,CAACyC,QAAQ,CAAC,EAAE;IACpD5B,MAAM,CAACY,IAAI,CAAC,UAAU,CAAC;EACzB;EAEA,IAAIiB,MAAM,CAACU,cAAc,IAAI,CAAC,cAAc,CAACpD,IAAI,CAACyC,QAAQ,CAAC,EAAE;IAC3D5B,MAAM,CAACY,IAAI,CAAC,YAAY,CAAC;EAC3B;EAEA,OAAOZ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwC,sBAAsBA,CAACC,cAAc,EAAE;EACrD,MAAMzC,MAAM,GAAG,EAAE;EAEjB,IAAI,CAACyC,cAAc,EAAE;IACnBzC,MAAM,CAACY,IAAI,CAAC,aAAa,CAAC;IAC1B,OAAOZ,MAAM;EACf;;EAEA;EACA,MAAM0C,KAAK,GAAGD,cAAc,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;EAChD,IAAIF,KAAK,CAAC3D,MAAM,KAAK,CAAC,IAAI2D,KAAK,CAAC3D,MAAM,KAAK,CAAC,EAAE;IAC5CiB,MAAM,CAACY,IAAI,CAAC,cAAc,CAAC;EAC7B;EAEA,OAAOZ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6C,YAAYA,CAACC,UAAU,EAAE;EACvC,MAAM9C,MAAM,GAAG,EAAE;EAEjB,IAAI,CAAC8C,UAAU,EAAE;IACf9C,MAAM,CAACY,IAAI,CAAC,UAAU,CAAC;IACvB,OAAOZ,MAAM;EACf;EAEA,IAAI;IACF+C,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;EACxB,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVjD,MAAM,CAACY,IAAI,CAAC,WAAW,CAAC;EAC1B;EAEA,OAAOZ,MAAM;AACf;AAEA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}