{"ast": null, "code": "import { checkPasswordStrength } from '@/utils/passwordUtils';\nexport default {\n  name: 'PasswordStrengthMeter',\n  props: {\n    password: {\n      type: String,\n      default: ''\n    },\n    showDetails: {\n      type: Boolean,\n      default: false\n    },\n    showSuggestions: {\n      type: Boolean,\n      default: false\n    },\n    policy: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  computed: {\n    strengthInfo() {\n      return checkPasswordStrength(this.password);\n    }\n  },\n  methods: {\n    getCheckLabel(key) {\n      const labels = {\n        length: '长度足够',\n        lowercase: '包含小写字母',\n        uppercase: '包含大写字母',\n        numbers: '包含数字',\n        special: '包含特殊字符',\n        noCommon: '非常见密码'\n      };\n      return labels[key] || key;\n    }\n  }\n};", "map": {"version": 3, "names": ["checkPasswordStrength", "name", "props", "password", "type", "String", "default", "showDetails", "Boolean", "showSuggestions", "policy", "Object", "computed", "strengthInfo", "methods", "getCheckLabel", "key", "labels", "length", "lowercase", "uppercase", "numbers", "special", "no<PERSON><PERSON><PERSON>"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue"], "sourcesContent": ["<template>\r\n  <div class=\"password-strength-meter\">\r\n    <!-- 强度指示条 -->\r\n    <div class=\"strength-bar-container\">\r\n      <div class=\"strength-bar\">\r\n        <div\r\n          class=\"strength-fill transition-all duration-300 ease-out\"\r\n          :style=\"{\r\n            width: `${(strengthInfo.score / strengthInfo.maxScore) * 100}%`,\r\n            backgroundColor: strengthInfo.color\r\n          }\"\r\n        ></div>\r\n      </div>\r\n      <div class=\"strength-segments\">\r\n        <div\r\n          v-for=\"i in 4\"\r\n          :key=\"i\"\r\n          class=\"strength-segment\"\r\n          :class=\"{\r\n            'active': i <= Math.ceil((strengthInfo.score / strengthInfo.maxScore) * 4),\r\n            'very-weak': strengthInfo.strength === 0,\r\n            'weak': strengthInfo.strength === 1,\r\n            'medium': strengthInfo.strength === 2,\r\n            'strong': strengthInfo.strength === 3,\r\n            'very-strong': strengthInfo.strength === 4\r\n          }\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 强度文本和分数 -->\r\n    <div class=\"flex justify-between items-center mt-2\">\r\n      <span\r\n        class=\"text-sm font-medium transition-colors duration-200\"\r\n        :style=\"{ color: strengthInfo.color }\"\r\n      >\r\n        {{ strengthInfo.label }}\r\n      </span>\r\n      <span class=\"text-xs text-gray-500 dark:text-gray-400\">\r\n        {{ strengthInfo.score }}/{{ strengthInfo.maxScore }}\r\n      </span>\r\n    </div>\r\n\r\n    <!-- 详细检查项 -->\r\n    <div v-if=\"showDetails && strengthInfo.checks\" class=\"mt-3 space-y-1\">\r\n      <div\r\n        v-for=\"(check, key) in strengthInfo.checks\"\r\n        :key=\"key\"\r\n        class=\"flex items-center text-xs\"\r\n      >\r\n        <font-awesome-icon\r\n          :icon=\"['fas', check ? 'check' : 'times']\"\r\n          :class=\"check ? 'text-green-500' : 'text-gray-400'\"\r\n          class=\"mr-2 w-3 h-3\"\r\n        />\r\n        <span :class=\"check ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'\">\r\n          {{ getCheckLabel(key) }}\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 改进建议 -->\r\n    <div v-if=\"showSuggestions && strengthInfo.feedback && strengthInfo.feedback.length > 0\" class=\"mt-3\">\r\n      <div class=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">改进建议：</div>\r\n      <ul class=\"text-xs space-y-1\">\r\n        <li\r\n          v-for=\"(suggestion, index) in strengthInfo.feedback\"\r\n          :key=\"index\"\r\n          class=\"flex items-start text-gray-600 dark:text-gray-400\"\r\n        >\r\n          <span class=\"mr-1\">•</span>\r\n          <span>{{ suggestion }}</span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { checkPasswordStrength } from '@/utils/passwordUtils'\r\n\r\nexport default {\r\n  name: 'PasswordStrengthMeter',\r\n  props: {\r\n    password: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    showDetails: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showSuggestions: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    policy: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  computed: {\r\n    strengthInfo() {\r\n      return checkPasswordStrength(this.password)\r\n    }\r\n  },\r\n  methods: {\r\n    getCheckLabel(key) {\r\n      const labels = {\r\n        length: '长度足够',\r\n        lowercase: '包含小写字母',\r\n        uppercase: '包含大写字母',\r\n        numbers: '包含数字',\r\n        special: '包含特殊字符',\r\n        noCommon: '非常见密码'\r\n      }\r\n      return labels[key] || key\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.password-strength-meter {\r\n  @apply w-full;\r\n}\r\n\r\n.strength-bar-container {\r\n  @apply relative;\r\n}\r\n\r\n.strength-bar {\r\n  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;\r\n}\r\n\r\n.strength-fill {\r\n  @apply h-full rounded-full;\r\n}\r\n\r\n.strength-segments {\r\n  @apply absolute top-0 left-0 w-full h-full flex;\r\n}\r\n\r\n.strength-segment {\r\n  @apply flex-1 border-r border-white dark:border-gray-800 last:border-r-0;\r\n  @apply transition-all duration-300;\r\n}\r\n\r\n.strength-segment.active.very-weak {\r\n  @apply bg-red-500;\r\n}\r\n\r\n.strength-segment.active.weak {\r\n  @apply bg-orange-500;\r\n}\r\n\r\n.strength-segment.active.medium {\r\n  @apply bg-yellow-500;\r\n}\r\n\r\n.strength-segment.active.strong {\r\n  @apply bg-green-500;\r\n}\r\n\r\n.strength-segment.active.very-strong {\r\n  @apply bg-green-600;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n.strength-fill {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.strength-segment.active {\r\n  animation: pulse 2s infinite;\r\n}\r\n</style>"], "mappings": "AA+EA,SAASA,qBAAoB,QAAS,uBAAsB;AAE5D,eAAe;EACbC,IAAI,EAAE,uBAAuB;EAC7BC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE;MACXH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDG,eAAe,EAAE;MACfL,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDI,MAAM,EAAE;MACNN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB;EACF,CAAC;EACDM,QAAQ,EAAE;IACRC,YAAYA,CAAA,EAAG;MACb,OAAOb,qBAAqB,CAAC,IAAI,CAACG,QAAQ;IAC5C;EACF,CAAC;EACDW,OAAO,EAAE;IACPC,aAAaA,CAACC,GAAG,EAAE;MACjB,MAAMC,MAAK,GAAI;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE,QAAQ;QACjBC,QAAQ,EAAE;MACZ;MACA,OAAON,MAAM,CAACD,GAAG,KAAKA,GAAE;IAC1B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}