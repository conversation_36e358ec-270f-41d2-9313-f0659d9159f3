{"ast": null, "code": "/**\n * 安全工具类\n * 提供数据加密、输入过滤、XSS防护等安全功能\n */\n\n/**\n * 安全工具类\n */\nexport class SecurityUtils {\n  constructor() {\n    this.setupCSP();\n  }\n\n  /**\n   * 设置内容安全策略\n   */\n  setupCSP() {\n    // 在实际应用中，CSP应该在服务器端设置\n    // 这里只是作为客户端的补充检查\n    if (!document.querySelector('meta[http-equiv=\"Content-Security-Policy\"]')) {\n      console.warn('建议设置Content-Security-Policy以增强安全性');\n    }\n  }\n\n  /**\n   * HTML转义，防止XSS攻击\n   * @param {string} str 需要转义的字符串\n   * @returns {string} 转义后的字符串\n   */\n  escapeHtml(str) {\n    if (typeof str !== 'string') return str;\n    const div = document.createElement('div');\n    div.textContent = str;\n    return div.innerHTML;\n  }\n\n  /**\n   * 移除HTML标签\n   * @param {string} str 包含HTML的字符串\n   * @returns {string} 纯文本字符串\n   */\n  stripHtml(str) {\n    if (typeof str !== 'string') return str;\n    const div = document.createElement('div');\n    div.innerHTML = str;\n    return div.textContent || div.innerText || '';\n  }\n\n  /**\n   * 验证和清理URL\n   * @param {string} url URL字符串\n   * @returns {string|null} 清理后的URL或null\n   */\n  sanitizeUrl(url) {\n    if (typeof url !== 'string') return null;\n    try {\n      const urlObj = new URL(url);\n\n      // 只允许http和https协议\n      if (!['http:', 'https:'].includes(urlObj.protocol)) {\n        return null;\n      }\n      return urlObj.href;\n    } catch {\n      return null;\n    }\n  }\n\n  /**\n   * 验证邮箱地址\n   * @param {string} email 邮箱地址\n   * @returns {boolean} 是否为有效邮箱\n   */\n  validateEmail(email) {\n    if (typeof email !== 'string') return false;\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email) && email.length <= 254;\n  }\n\n  /**\n   * 验证IP地址\n   * @param {string} ip IP地址\n   * @returns {boolean} 是否为有效IP\n   */\n  validateIP(ip) {\n    if (typeof ip !== 'string') return false;\n\n    // IPv4验证\n    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n    if (ipv4Regex.test(ip)) return true;\n\n    // IPv6验证（简化版）\n    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;\n    return ipv6Regex.test(ip);\n  }\n\n  /**\n   * 生成随机字符串\n   * @param {number} length 字符串长度\n   * @param {string} charset 字符集\n   * @returns {string} 随机字符串\n   */\n  generateRandomString(length = 16, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {\n    let result = '';\n    const array = new Uint8Array(length);\n    crypto.getRandomValues(array);\n    for (let i = 0; i < length; i++) {\n      result += charset[array[i] % charset.length];\n    }\n    return result;\n  }\n\n  /**\n   * 生成UUID\n   * @returns {string} UUID字符串\n   */\n  generateUUID() {\n    if (crypto.randomUUID) {\n      return crypto.randomUUID();\n    }\n\n    // 降级方案\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0;\n      const v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n\n  /**\n   * 简单的字符串哈希\n   * @param {string} str 要哈希的字符串\n   * @returns {string} 哈希值\n   */\n  simpleHash(str) {\n    let hash = 0;\n    if (str.length === 0) return hash.toString();\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = (hash << 5) - hash + char;\n      hash = hash & hash; // 转换为32位整数\n    }\n    return Math.abs(hash).toString(16);\n  }\n\n  /**\n   * 检查密码强度\n   * @param {string} password 密码\n   * @returns {Object} 强度信息\n   */\n  checkPasswordSecurity(password) {\n    const issues = [];\n    let score = 0;\n    if (!password) {\n      return {\n        score: 0,\n        issues: ['密码不能为空'],\n        level: 'critical'\n      };\n    }\n\n    // 长度检查\n    if (password.length < 8) {\n      issues.push('密码长度至少8位');\n    } else {\n      score += 20;\n    }\n    if (password.length >= 12) score += 10;\n    if (password.length >= 16) score += 10;\n\n    // 字符类型检查\n    if (/[a-z]/.test(password)) score += 10;else issues.push('缺少小写字母');\n    if (/[A-Z]/.test(password)) score += 10;else issues.push('缺少大写字母');\n    if (/[0-9]/.test(password)) score += 10;else issues.push('缺少数字');\n    if (/[^a-zA-Z0-9]/.test(password)) score += 15;else issues.push('缺少特殊字符');\n\n    // 复杂度检查\n    if (!/(.)\\1{2,}/.test(password)) score += 10;else issues.push('避免连续重复字符');\n\n    // 常见密码检查\n    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root'];\n    if (!commonPasswords.some(common => password.toLowerCase().includes(common))) {\n      score += 15;\n    } else {\n      issues.push('避免使用常见密码');\n    }\n\n    // 确定安全级别\n    let level = 'critical';\n    if (score >= 80) level = 'high';else if (score >= 60) level = 'medium';else if (score >= 40) level = 'low';\n    return {\n      score,\n      issues,\n      level\n    };\n  }\n\n  /**\n   * 输入过滤和清理\n   * @param {string} input 用户输入\n   * @param {Object} options 过滤选项\n   * @returns {string} 清理后的输入\n   */\n  sanitizeInput(input, options = {}) {\n    if (typeof input !== 'string') return '';\n    const {\n      maxLength = 1000,\n      allowHtml = false,\n      allowSpecialChars = true,\n      trimWhitespace = true\n    } = options;\n    let sanitized = input;\n\n    // 长度限制\n    if (sanitized.length > maxLength) {\n      sanitized = sanitized.substring(0, maxLength);\n    }\n\n    // 去除首尾空白\n    if (trimWhitespace) {\n      sanitized = sanitized.trim();\n    }\n\n    // HTML处理\n    if (!allowHtml) {\n      sanitized = this.escapeHtml(sanitized);\n    }\n\n    // 特殊字符处理\n    if (!allowSpecialChars) {\n      sanitized = sanitized.replace(/[<>'\"&]/g, '');\n    }\n    return sanitized;\n  }\n\n  /**\n   * 检查是否为可疑的用户代理\n   * @param {string} userAgent 用户代理字符串\n   * @returns {boolean} 是否可疑\n   */\n  isSuspiciousUserAgent(userAgent) {\n    if (!userAgent) return true;\n    const suspiciousPatterns = [/bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i, /python/i, /java/i];\n    return suspiciousPatterns.some(pattern => pattern.test(userAgent));\n  }\n\n  /**\n   * 生成CSRF令牌\n   * @returns {string} CSRF令牌\n   */\n  generateCSRFToken() {\n    return this.generateRandomString(32);\n  }\n\n  /**\n   * 验证CSRF令牌\n   * @param {string} token 提交的令牌\n   * @param {string} expectedToken 期望的令牌\n   * @returns {boolean} 是否有效\n   */\n  validateCSRFToken(token, expectedToken) {\n    return token === expectedToken && token.length === 32;\n  }\n\n  /**\n   * 检查请求频率限制\n   * @param {string} identifier 标识符（如IP地址）\n   * @param {number} maxRequests 最大请求数\n   * @param {number} timeWindow 时间窗口（毫秒）\n   * @returns {boolean} 是否允许请求\n   */\n  checkRateLimit(identifier, maxRequests = 100, timeWindow = 60000) {\n    const now = Date.now();\n    const key = `rateLimit_${identifier}`;\n    let requests = JSON.parse(localStorage.getItem(key) || '[]');\n\n    // 清理过期的请求记录\n    requests = requests.filter(timestamp => now - timestamp < timeWindow);\n\n    // 检查是否超过限制\n    if (requests.length >= maxRequests) {\n      return false;\n    }\n\n    // 记录当前请求\n    requests.push(now);\n    localStorage.setItem(key, JSON.stringify(requests));\n    return true;\n  }\n\n  /**\n   * 安全的JSON解析\n   * @param {string} jsonString JSON字符串\n   * @returns {Object|null} 解析结果或null\n   */\n  safeJSONParse(jsonString) {\n    try {\n      if (typeof jsonString !== 'string') return null;\n\n      // 检查JSON字符串长度\n      if (jsonString.length > 1024 * 1024) {\n        // 1MB限制\n        console.warn('JSON字符串过大');\n        return null;\n      }\n      return JSON.parse(jsonString);\n    } catch {\n      return null;\n    }\n  }\n\n  /**\n   * 清理对象中的敏感信息\n   * @param {Object} obj 对象\n   * @param {Array} sensitiveKeys 敏感字段名\n   * @returns {Object} 清理后的对象\n   */\n  sanitizeObject(obj, sensitiveKeys = ['password', 'token', 'secret', 'key']) {\n    if (!obj || typeof obj !== 'object') return obj;\n    const cleaned = {\n      ...obj\n    };\n    sensitiveKeys.forEach(key => {\n      if (key in cleaned) {\n        cleaned[key] = '[REDACTED]';\n      }\n    });\n    return cleaned;\n  }\n}\n\n// 创建全局安全工具实例\nexport const security = new SecurityUtils();\n\n// 便捷函数导出\nexport const {\n  escapeHtml,\n  stripHtml,\n  sanitizeUrl,\n  validateEmail,\n  validateIP,\n  generateRandomString,\n  generateUUID,\n  simpleHash,\n  checkPasswordSecurity,\n  sanitizeInput,\n  generateCSRFToken,\n  validateCSRFToken,\n  checkRateLimit,\n  safeJSONParse,\n  sanitizeObject\n} = security;\nexport default security;", "map": {"version": 3, "names": ["SecurityUtils", "constructor", "setupCSP", "document", "querySelector", "console", "warn", "escapeHtml", "str", "div", "createElement", "textContent", "innerHTML", "stripHtml", "innerText", "sanitizeUrl", "url", "url<PERSON>bj", "URL", "includes", "protocol", "href", "validateEmail", "email", "emailRegex", "test", "length", "validateIP", "ip", "ipv4Regex", "ipv6Regex", "generateRandomString", "charset", "result", "array", "Uint8Array", "crypto", "getRandomValues", "i", "generateUUID", "randomUUID", "replace", "c", "r", "Math", "random", "v", "toString", "simpleHash", "hash", "char", "charCodeAt", "abs", "checkPasswordSecurity", "password", "issues", "score", "level", "push", "commonPasswords", "some", "common", "toLowerCase", "sanitizeInput", "input", "options", "max<PERSON><PERSON><PERSON>", "allowHtml", "allowSpecialChars", "trimWhitespace", "sanitized", "substring", "trim", "isSuspiciousUserAgent", "userAgent", "suspiciousPatterns", "pattern", "generateCSRFToken", "validateCSRFToken", "token", "expectedToken", "checkRateLimit", "identifier", "maxRequests", "timeWindow", "now", "Date", "key", "requests", "JSON", "parse", "localStorage", "getItem", "filter", "timestamp", "setItem", "stringify", "safeJSONParse", "jsonString", "sanitizeObject", "obj", "<PERSON><PERSON><PERSON><PERSON>", "cleaned", "for<PERSON>ach", "security"], "sources": ["D:/demo/ooo/pass/src/utils/security.js"], "sourcesContent": ["/**\n * 安全工具类\n * 提供数据加密、输入过滤、XSS防护等安全功能\n */\n\n/**\n * 安全工具类\n */\nexport class SecurityUtils {\n  constructor() {\n    this.setupCSP()\n  }\n\n  /**\n   * 设置内容安全策略\n   */\n  setupCSP() {\n    // 在实际应用中，CSP应该在服务器端设置\n    // 这里只是作为客户端的补充检查\n    if (!document.querySelector('meta[http-equiv=\"Content-Security-Policy\"]')) {\n      console.warn('建议设置Content-Security-Policy以增强安全性')\n    }\n  }\n\n  /**\n   * HTML转义，防止XSS攻击\n   * @param {string} str 需要转义的字符串\n   * @returns {string} 转义后的字符串\n   */\n  escapeHtml(str) {\n    if (typeof str !== 'string') return str\n    \n    const div = document.createElement('div')\n    div.textContent = str\n    return div.innerHTML\n  }\n\n  /**\n   * 移除HTML标签\n   * @param {string} str 包含HTML的字符串\n   * @returns {string} 纯文本字符串\n   */\n  stripHtml(str) {\n    if (typeof str !== 'string') return str\n    \n    const div = document.createElement('div')\n    div.innerHTML = str\n    return div.textContent || div.innerText || ''\n  }\n\n  /**\n   * 验证和清理URL\n   * @param {string} url URL字符串\n   * @returns {string|null} 清理后的URL或null\n   */\n  sanitizeUrl(url) {\n    if (typeof url !== 'string') return null\n    \n    try {\n      const urlObj = new URL(url)\n      \n      // 只允许http和https协议\n      if (!['http:', 'https:'].includes(urlObj.protocol)) {\n        return null\n      }\n      \n      return urlObj.href\n    } catch {\n      return null\n    }\n  }\n\n  /**\n   * 验证邮箱地址\n   * @param {string} email 邮箱地址\n   * @returns {boolean} 是否为有效邮箱\n   */\n  validateEmail(email) {\n    if (typeof email !== 'string') return false\n    \n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    return emailRegex.test(email) && email.length <= 254\n  }\n\n  /**\n   * 验证IP地址\n   * @param {string} ip IP地址\n   * @returns {boolean} 是否为有效IP\n   */\n  validateIP(ip) {\n    if (typeof ip !== 'string') return false\n    \n    // IPv4验证\n    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n    if (ipv4Regex.test(ip)) return true\n    \n    // IPv6验证（简化版）\n    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/\n    return ipv6Regex.test(ip)\n  }\n\n  /**\n   * 生成随机字符串\n   * @param {number} length 字符串长度\n   * @param {string} charset 字符集\n   * @returns {string} 随机字符串\n   */\n  generateRandomString(length = 16, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {\n    let result = ''\n    const array = new Uint8Array(length)\n    crypto.getRandomValues(array)\n    \n    for (let i = 0; i < length; i++) {\n      result += charset[array[i] % charset.length]\n    }\n    \n    return result\n  }\n\n  /**\n   * 生成UUID\n   * @returns {string} UUID字符串\n   */\n  generateUUID() {\n    if (crypto.randomUUID) {\n      return crypto.randomUUID()\n    }\n    \n    // 降级方案\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0\n      const v = c === 'x' ? r : (r & 0x3 | 0x8)\n      return v.toString(16)\n    })\n  }\n\n  /**\n   * 简单的字符串哈希\n   * @param {string} str 要哈希的字符串\n   * @returns {string} 哈希值\n   */\n  simpleHash(str) {\n    let hash = 0\n    if (str.length === 0) return hash.toString()\n    \n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i)\n      hash = ((hash << 5) - hash) + char\n      hash = hash & hash // 转换为32位整数\n    }\n    \n    return Math.abs(hash).toString(16)\n  }\n\n  /**\n   * 检查密码强度\n   * @param {string} password 密码\n   * @returns {Object} 强度信息\n   */\n  checkPasswordSecurity(password) {\n    const issues = []\n    let score = 0\n    \n    if (!password) {\n      return { score: 0, issues: ['密码不能为空'], level: 'critical' }\n    }\n    \n    // 长度检查\n    if (password.length < 8) {\n      issues.push('密码长度至少8位')\n    } else {\n      score += 20\n    }\n    \n    if (password.length >= 12) score += 10\n    if (password.length >= 16) score += 10\n    \n    // 字符类型检查\n    if (/[a-z]/.test(password)) score += 10\n    else issues.push('缺少小写字母')\n    \n    if (/[A-Z]/.test(password)) score += 10\n    else issues.push('缺少大写字母')\n    \n    if (/[0-9]/.test(password)) score += 10\n    else issues.push('缺少数字')\n    \n    if (/[^a-zA-Z0-9]/.test(password)) score += 15\n    else issues.push('缺少特殊字符')\n    \n    // 复杂度检查\n    if (!/(.)\\1{2,}/.test(password)) score += 10\n    else issues.push('避免连续重复字符')\n    \n    // 常见密码检查\n    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root']\n    if (!commonPasswords.some(common => password.toLowerCase().includes(common))) {\n      score += 15\n    } else {\n      issues.push('避免使用常见密码')\n    }\n    \n    // 确定安全级别\n    let level = 'critical'\n    if (score >= 80) level = 'high'\n    else if (score >= 60) level = 'medium'\n    else if (score >= 40) level = 'low'\n    \n    return { score, issues, level }\n  }\n\n  /**\n   * 输入过滤和清理\n   * @param {string} input 用户输入\n   * @param {Object} options 过滤选项\n   * @returns {string} 清理后的输入\n   */\n  sanitizeInput(input, options = {}) {\n    if (typeof input !== 'string') return ''\n    \n    const {\n      maxLength = 1000,\n      allowHtml = false,\n      allowSpecialChars = true,\n      trimWhitespace = true\n    } = options\n    \n    let sanitized = input\n    \n    // 长度限制\n    if (sanitized.length > maxLength) {\n      sanitized = sanitized.substring(0, maxLength)\n    }\n    \n    // 去除首尾空白\n    if (trimWhitespace) {\n      sanitized = sanitized.trim()\n    }\n    \n    // HTML处理\n    if (!allowHtml) {\n      sanitized = this.escapeHtml(sanitized)\n    }\n    \n    // 特殊字符处理\n    if (!allowSpecialChars) {\n      sanitized = sanitized.replace(/[<>'\"&]/g, '')\n    }\n    \n    return sanitized\n  }\n\n  /**\n   * 检查是否为可疑的用户代理\n   * @param {string} userAgent 用户代理字符串\n   * @returns {boolean} 是否可疑\n   */\n  isSuspiciousUserAgent(userAgent) {\n    if (!userAgent) return true\n    \n    const suspiciousPatterns = [\n      /bot/i,\n      /crawler/i,\n      /spider/i,\n      /scraper/i,\n      /curl/i,\n      /wget/i,\n      /python/i,\n      /java/i\n    ]\n    \n    return suspiciousPatterns.some(pattern => pattern.test(userAgent))\n  }\n\n  /**\n   * 生成CSRF令牌\n   * @returns {string} CSRF令牌\n   */\n  generateCSRFToken() {\n    return this.generateRandomString(32)\n  }\n\n  /**\n   * 验证CSRF令牌\n   * @param {string} token 提交的令牌\n   * @param {string} expectedToken 期望的令牌\n   * @returns {boolean} 是否有效\n   */\n  validateCSRFToken(token, expectedToken) {\n    return token === expectedToken && token.length === 32\n  }\n\n  /**\n   * 检查请求频率限制\n   * @param {string} identifier 标识符（如IP地址）\n   * @param {number} maxRequests 最大请求数\n   * @param {number} timeWindow 时间窗口（毫秒）\n   * @returns {boolean} 是否允许请求\n   */\n  checkRateLimit(identifier, maxRequests = 100, timeWindow = 60000) {\n    const now = Date.now()\n    const key = `rateLimit_${identifier}`\n    \n    let requests = JSON.parse(localStorage.getItem(key) || '[]')\n    \n    // 清理过期的请求记录\n    requests = requests.filter(timestamp => now - timestamp < timeWindow)\n    \n    // 检查是否超过限制\n    if (requests.length >= maxRequests) {\n      return false\n    }\n    \n    // 记录当前请求\n    requests.push(now)\n    localStorage.setItem(key, JSON.stringify(requests))\n    \n    return true\n  }\n\n  /**\n   * 安全的JSON解析\n   * @param {string} jsonString JSON字符串\n   * @returns {Object|null} 解析结果或null\n   */\n  safeJSONParse(jsonString) {\n    try {\n      if (typeof jsonString !== 'string') return null\n      \n      // 检查JSON字符串长度\n      if (jsonString.length > 1024 * 1024) { // 1MB限制\n        console.warn('JSON字符串过大')\n        return null\n      }\n      \n      return JSON.parse(jsonString)\n    } catch {\n      return null\n    }\n  }\n\n  /**\n   * 清理对象中的敏感信息\n   * @param {Object} obj 对象\n   * @param {Array} sensitiveKeys 敏感字段名\n   * @returns {Object} 清理后的对象\n   */\n  sanitizeObject(obj, sensitiveKeys = ['password', 'token', 'secret', 'key']) {\n    if (!obj || typeof obj !== 'object') return obj\n    \n    const cleaned = { ...obj }\n    \n    sensitiveKeys.forEach(key => {\n      if (key in cleaned) {\n        cleaned[key] = '[REDACTED]'\n      }\n    })\n    \n    return cleaned\n  }\n}\n\n// 创建全局安全工具实例\nexport const security = new SecurityUtils()\n\n// 便捷函数导出\nexport const {\n  escapeHtml,\n  stripHtml,\n  sanitizeUrl,\n  validateEmail,\n  validateIP,\n  generateRandomString,\n  generateUUID,\n  simpleHash,\n  checkPasswordSecurity,\n  sanitizeInput,\n  generateCSRFToken,\n  validateCSRFToken,\n  checkRateLimit,\n  safeJSONParse,\n  sanitizeObject\n} = security\n\nexport default security\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB;;EAEA;AACF;AACA;EACEA,QAAQA,CAAA,EAAG;IACT;IACA;IACA,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC,4CAA4C,CAAC,EAAE;MACzEC,OAAO,CAACC,IAAI,CAAC,mCAAmC,CAAC;IACnD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAACC,GAAG,EAAE;IACd,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAEvC,MAAMC,GAAG,GAAGN,QAAQ,CAACO,aAAa,CAAC,KAAK,CAAC;IACzCD,GAAG,CAACE,WAAW,GAAGH,GAAG;IACrB,OAAOC,GAAG,CAACG,SAAS;EACtB;;EAEA;AACF;AACA;AACA;AACA;EACEC,SAASA,CAACL,GAAG,EAAE;IACb,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAEvC,MAAMC,GAAG,GAAGN,QAAQ,CAACO,aAAa,CAAC,KAAK,CAAC;IACzCD,GAAG,CAACG,SAAS,GAAGJ,GAAG;IACnB,OAAOC,GAAG,CAACE,WAAW,IAAIF,GAAG,CAACK,SAAS,IAAI,EAAE;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,GAAG,EAAE;IACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,IAAI;IAExC,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAACF,GAAG,CAAC;;MAE3B;MACA,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACG,QAAQ,CAACF,MAAM,CAACG,QAAQ,CAAC,EAAE;QAClD,OAAO,IAAI;MACb;MAEA,OAAOH,MAAM,CAACI,IAAI;IACpB,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEC,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK;IAE3C,MAAMC,UAAU,GAAG,4BAA4B;IAC/C,OAAOA,UAAU,CAACC,IAAI,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,IAAI,GAAG;EACtD;;EAEA;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAACC,EAAE,EAAE;IACb,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK;;IAExC;IACA,MAAMC,SAAS,GAAG,6FAA6F;IAC/G,IAAIA,SAAS,CAACJ,IAAI,CAACG,EAAE,CAAC,EAAE,OAAO,IAAI;;IAEnC;IACA,MAAME,SAAS,GAAG,4CAA4C;IAC9D,OAAOA,SAAS,CAACL,IAAI,CAACG,EAAE,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEG,oBAAoBA,CAACL,MAAM,GAAG,EAAE,EAAEM,OAAO,GAAG,gEAAgE,EAAE;IAC5G,IAAIC,MAAM,GAAG,EAAE;IACf,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACT,MAAM,CAAC;IACpCU,MAAM,CAACC,eAAe,CAACH,KAAK,CAAC;IAE7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,EAAEY,CAAC,EAAE,EAAE;MAC/BL,MAAM,IAAID,OAAO,CAACE,KAAK,CAACI,CAAC,CAAC,GAAGN,OAAO,CAACN,MAAM,CAAC;IAC9C;IAEA,OAAOO,MAAM;EACf;;EAEA;AACF;AACA;AACA;EACEM,YAAYA,CAAA,EAAG;IACb,IAAIH,MAAM,CAACI,UAAU,EAAE;MACrB,OAAOJ,MAAM,CAACI,UAAU,CAAC,CAAC;IAC5B;;IAEA;IACA,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;MACzE,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;MAChC,MAAMC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACzC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAACxC,GAAG,EAAE;IACd,IAAIyC,IAAI,GAAG,CAAC;IACZ,IAAIzC,GAAG,CAACkB,MAAM,KAAK,CAAC,EAAE,OAAOuB,IAAI,CAACF,QAAQ,CAAC,CAAC;IAE5C,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,GAAG,CAACkB,MAAM,EAAEY,CAAC,EAAE,EAAE;MACnC,MAAMY,IAAI,GAAG1C,GAAG,CAAC2C,UAAU,CAACb,CAAC,CAAC;MAC9BW,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIC,IAAI;MAClCD,IAAI,GAAGA,IAAI,GAAGA,IAAI,EAAC;IACrB;IAEA,OAAOL,IAAI,CAACQ,GAAG,CAACH,IAAI,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC;EACpC;;EAEA;AACF;AACA;AACA;AACA;EACEM,qBAAqBA,CAACC,QAAQ,EAAE;IAC9B,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,KAAK,GAAG,CAAC;IAEb,IAAI,CAACF,QAAQ,EAAE;MACb,OAAO;QAAEE,KAAK,EAAE,CAAC;QAAED,MAAM,EAAE,CAAC,QAAQ,CAAC;QAAEE,KAAK,EAAE;MAAW,CAAC;IAC5D;;IAEA;IACA,IAAIH,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAE;MACvB6B,MAAM,CAACG,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC,MAAM;MACLF,KAAK,IAAI,EAAE;IACb;IAEA,IAAIF,QAAQ,CAAC5B,MAAM,IAAI,EAAE,EAAE8B,KAAK,IAAI,EAAE;IACtC,IAAIF,QAAQ,CAAC5B,MAAM,IAAI,EAAE,EAAE8B,KAAK,IAAI,EAAE;;IAEtC;IACA,IAAI,OAAO,CAAC/B,IAAI,CAAC6B,QAAQ,CAAC,EAAEE,KAAK,IAAI,EAAE,MAClCD,MAAM,CAACG,IAAI,CAAC,QAAQ,CAAC;IAE1B,IAAI,OAAO,CAACjC,IAAI,CAAC6B,QAAQ,CAAC,EAAEE,KAAK,IAAI,EAAE,MAClCD,MAAM,CAACG,IAAI,CAAC,QAAQ,CAAC;IAE1B,IAAI,OAAO,CAACjC,IAAI,CAAC6B,QAAQ,CAAC,EAAEE,KAAK,IAAI,EAAE,MAClCD,MAAM,CAACG,IAAI,CAAC,MAAM,CAAC;IAExB,IAAI,cAAc,CAACjC,IAAI,CAAC6B,QAAQ,CAAC,EAAEE,KAAK,IAAI,EAAE,MACzCD,MAAM,CAACG,IAAI,CAAC,QAAQ,CAAC;;IAE1B;IACA,IAAI,CAAC,WAAW,CAACjC,IAAI,CAAC6B,QAAQ,CAAC,EAAEE,KAAK,IAAI,EAAE,MACvCD,MAAM,CAACG,IAAI,CAAC,UAAU,CAAC;;IAE5B;IACA,MAAMC,eAAe,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;IACzE,IAAI,CAACA,eAAe,CAACC,IAAI,CAACC,MAAM,IAAIP,QAAQ,CAACQ,WAAW,CAAC,CAAC,CAAC3C,QAAQ,CAAC0C,MAAM,CAAC,CAAC,EAAE;MAC5EL,KAAK,IAAI,EAAE;IACb,CAAC,MAAM;MACLD,MAAM,CAACG,IAAI,CAAC,UAAU,CAAC;IACzB;;IAEA;IACA,IAAID,KAAK,GAAG,UAAU;IACtB,IAAID,KAAK,IAAI,EAAE,EAAEC,KAAK,GAAG,MAAM,MAC1B,IAAID,KAAK,IAAI,EAAE,EAAEC,KAAK,GAAG,QAAQ,MACjC,IAAID,KAAK,IAAI,EAAE,EAAEC,KAAK,GAAG,KAAK;IAEnC,OAAO;MAAED,KAAK;MAAED,MAAM;MAAEE;IAAM,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEM,aAAaA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE,OAAO,EAAE;IAExC,MAAM;MACJE,SAAS,GAAG,IAAI;MAChBC,SAAS,GAAG,KAAK;MACjBC,iBAAiB,GAAG,IAAI;MACxBC,cAAc,GAAG;IACnB,CAAC,GAAGJ,OAAO;IAEX,IAAIK,SAAS,GAAGN,KAAK;;IAErB;IACA,IAAIM,SAAS,CAAC5C,MAAM,GAAGwC,SAAS,EAAE;MAChCI,SAAS,GAAGA,SAAS,CAACC,SAAS,CAAC,CAAC,EAAEL,SAAS,CAAC;IAC/C;;IAEA;IACA,IAAIG,cAAc,EAAE;MAClBC,SAAS,GAAGA,SAAS,CAACE,IAAI,CAAC,CAAC;IAC9B;;IAEA;IACA,IAAI,CAACL,SAAS,EAAE;MACdG,SAAS,GAAG,IAAI,CAAC/D,UAAU,CAAC+D,SAAS,CAAC;IACxC;;IAEA;IACA,IAAI,CAACF,iBAAiB,EAAE;MACtBE,SAAS,GAAGA,SAAS,CAAC7B,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAC/C;IAEA,OAAO6B,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACEG,qBAAqBA,CAACC,SAAS,EAAE;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,kBAAkB,GAAG,CACzB,MAAM,EACN,UAAU,EACV,SAAS,EACT,UAAU,EACV,OAAO,EACP,OAAO,EACP,SAAS,EACT,OAAO,CACR;IAED,OAAOA,kBAAkB,CAACf,IAAI,CAACgB,OAAO,IAAIA,OAAO,CAACnD,IAAI,CAACiD,SAAS,CAAC,CAAC;EACpE;;EAEA;AACF;AACA;AACA;EACEG,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9C,oBAAoB,CAAC,EAAE,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE+C,iBAAiBA,CAACC,KAAK,EAAEC,aAAa,EAAE;IACtC,OAAOD,KAAK,KAAKC,aAAa,IAAID,KAAK,CAACrD,MAAM,KAAK,EAAE;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEuD,cAAcA,CAACC,UAAU,EAAEC,WAAW,GAAG,GAAG,EAAEC,UAAU,GAAG,KAAK,EAAE;IAChE,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,GAAG,GAAG,aAAaL,UAAU,EAAE;IAErC,IAAIM,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC,IAAI,IAAI,CAAC;;IAE5D;IACAC,QAAQ,GAAGA,QAAQ,CAACK,MAAM,CAACC,SAAS,IAAIT,GAAG,GAAGS,SAAS,GAAGV,UAAU,CAAC;;IAErE;IACA,IAAII,QAAQ,CAAC9D,MAAM,IAAIyD,WAAW,EAAE;MAClC,OAAO,KAAK;IACd;;IAEA;IACAK,QAAQ,CAAC9B,IAAI,CAAC2B,GAAG,CAAC;IAClBM,YAAY,CAACI,OAAO,CAACR,GAAG,EAAEE,IAAI,CAACO,SAAS,CAACR,QAAQ,CAAC,CAAC;IAEnD,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACES,aAAaA,CAACC,UAAU,EAAE;IACxB,IAAI;MACF,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE,OAAO,IAAI;;MAE/C;MACA,IAAIA,UAAU,CAACxE,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE;QAAE;QACrCrB,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;QACzB,OAAO,IAAI;MACb;MAEA,OAAOmF,IAAI,CAACC,KAAK,CAACQ,UAAU,CAAC;IAC/B,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,cAAcA,CAACC,GAAG,EAAEC,aAAa,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE;IAC1E,IAAI,CAACD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAE/C,MAAME,OAAO,GAAG;MAAE,GAAGF;IAAI,CAAC;IAE1BC,aAAa,CAACE,OAAO,CAAChB,GAAG,IAAI;MAC3B,IAAIA,GAAG,IAAIe,OAAO,EAAE;QAClBA,OAAO,CAACf,GAAG,CAAC,GAAG,YAAY;MAC7B;IACF,CAAC,CAAC;IAEF,OAAOe,OAAO;EAChB;AACF;;AAEA;AACA,OAAO,MAAME,QAAQ,GAAG,IAAIxG,aAAa,CAAC,CAAC;;AAE3C;AACA,OAAO,MAAM;EACXO,UAAU;EACVM,SAAS;EACTE,WAAW;EACXO,aAAa;EACbK,UAAU;EACVI,oBAAoB;EACpBQ,YAAY;EACZS,UAAU;EACVK,qBAAqB;EACrBU,aAAa;EACbc,iBAAiB;EACjBC,iBAAiB;EACjBG,cAAc;EACdgB,aAAa;EACbE;AACF,CAAC,GAAGK,QAAQ;AAEZ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}