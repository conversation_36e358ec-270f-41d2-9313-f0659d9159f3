[{"D:\\demo\\ooo\\pass\\src\\main.js": "1", "D:\\demo\\ooo\\pass\\src\\App.vue": "2", "D:\\demo\\ooo\\pass\\src\\router\\index.js": "3", "D:\\demo\\ooo\\pass\\src\\store\\index.js": "4", "D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue": "5", "D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue": "6", "D:\\demo\\ooo\\pass\\src\\views\\SecurityOverview.vue": "7", "D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue": "8", "D:\\demo\\ooo\\pass\\src\\components\\NotificationSystem.vue": "9", "D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue": "10", "D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue": "11", "D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue": "12", "D:\\demo\\ooo\\pass\\src\\components\\SecurityDashboard.vue": "13", "D:\\demo\\ooo\\pass\\src\\components\\AdvancedPasswordGenerator.vue": "14", "D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue": "15", "D:\\demo\\ooo\\pass\\src\\utils\\notification.js": "16", "D:\\demo\\ooo\\pass\\src\\views\\NotificationTest.vue": "17", "D:\\demo\\ooo\\pass\\src\\utils\\passwordUtils.js": "18", "D:\\demo\\ooo\\pass\\src\\utils\\errorHandler.js": "19", "D:\\demo\\ooo\\pass\\src\\utils\\validation.js": "20", "D:\\demo\\ooo\\pass\\src\\utils\\security.js": "21"}, {"size": 1709, "mtime": 1749111612365, "results": "22", "hashOfConfig": "23"}, {"size": 17590, "mtime": 1749110423026, "results": "24", "hashOfConfig": "23"}, {"size": 1574, "mtime": 1749111951640, "results": "25", "hashOfConfig": "23"}, {"size": 14072, "mtime": 1745398750821, "results": "26", "hashOfConfig": "23"}, {"size": 16724, "mtime": 1745393320344, "results": "27", "hashOfConfig": "23"}, {"size": 75259, "mtime": 1749116640512, "results": "28", "hashOfConfig": "23"}, {"size": 13193, "mtime": 1749112273463, "results": "29", "hashOfConfig": "23"}, {"size": 20744, "mtime": 1745394543431, "results": "30", "hashOfConfig": "23"}, {"size": 8478, "mtime": 1749111643746, "results": "31", "hashOfConfig": "23"}, {"size": 639, "mtime": 1745389673039, "results": "32", "hashOfConfig": "23"}, {"size": 5242, "mtime": 1745394639794, "results": "33", "hashOfConfig": "23"}, {"size": 4602, "mtime": 1749116292245, "results": "34", "hashOfConfig": "23"}, {"size": 13264, "mtime": 1749110134045, "results": "35", "hashOfConfig": "23"}, {"size": 12440, "mtime": 1749116386671, "results": "36", "hashOfConfig": "23"}, {"size": 512, "mtime": 1745389662414, "results": "37", "hashOfConfig": "23"}, {"size": 3458, "mtime": 1749111680030, "results": "38", "hashOfConfig": "23"}, {"size": 8783, "mtime": 1749111925030, "results": "39", "hashOfConfig": "23"}, {"size": 12214, "mtime": 1749116169458, "results": "40", "hashOfConfig": "23"}, {"size": 8121, "mtime": 1749116211224, "results": "41", "hashOfConfig": "23"}, {"size": 8679, "mtime": 1749116247133, "results": "42", "hashOfConfig": "23"}, {"size": 9235, "mtime": 1749116481761, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, "16pt2e3", {"filePath": "47", "messages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "56", "messages": "57", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\demo\\ooo\\pass\\src\\main.js", [], [], "D:\\demo\\ooo\\pass\\src\\App.vue", [], [], "D:\\demo\\ooo\\pass\\src\\router\\index.js", [], "D:\\demo\\ooo\\pass\\src\\store\\index.js", [], "D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue", ["88", "89", "90", "91"], "D:\\demo\\ooo\\pass\\src\\views\\SecurityOverview.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\NotificationSystem.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\SecurityDashboard.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\AdvancedPasswordGenerator.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue", [], "D:\\demo\\ooo\\pass\\src\\utils\\notification.js", [], "D:\\demo\\ooo\\pass\\src\\views\\NotificationTest.vue", [], "D:\\demo\\ooo\\pass\\src\\utils\\passwordUtils.js", [], "D:\\demo\\ooo\\pass\\src\\utils\\errorHandler.js", [], "D:\\demo\\ooo\\pass\\src\\utils\\validation.js", [], "D:\\demo\\ooo\\pass\\src\\utils\\security.js", [], {"ruleId": "92", "severity": 2, "message": "93", "line": 856, "column": 34, "nodeType": "94", "messageId": "95", "endLine": 856, "endColumn": 45}, {"ruleId": "92", "severity": 2, "message": "96", "line": 857, "column": 10, "nodeType": "94", "messageId": "95", "endLine": 857, "endColumn": 23}, {"ruleId": "92", "severity": 2, "message": "97", "line": 859, "column": 10, "nodeType": "94", "messageId": "95", "endLine": 859, "endColumn": 33}, {"ruleId": "92", "severity": 2, "message": "98", "line": 860, "column": 25, "nodeType": "94", "messageId": "95", "endLine": 860, "endColumn": 35}, "no-unused-vars", "'showWarning' is defined but never used.", "Identifier", "unusedVar", "'validateField' is defined but never used.", "'calculatePasswordExpiry' is defined but never used.", "'escapeHtml' is defined but never used."]