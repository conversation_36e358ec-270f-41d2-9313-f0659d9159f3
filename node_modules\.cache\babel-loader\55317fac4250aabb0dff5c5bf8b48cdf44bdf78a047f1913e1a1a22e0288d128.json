{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"password-strength-meter\"\n};\nconst _hoisted_2 = {\n  class: \"strength-bar-container\"\n};\nconst _hoisted_3 = {\n  class: \"strength-bar\"\n};\nconst _hoisted_4 = {\n  class: \"strength-segments\"\n};\nconst _hoisted_5 = {\n  class: \"flex justify-between items-center mt-2\"\n};\nconst _hoisted_6 = {\n  class: \"text-xs text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"mt-3 space-y-1\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"mt-3\"\n};\nconst _hoisted_9 = {\n  class: \"text-xs space-y-1\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 强度指示条 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", {\n    class: \"strength-fill transition-all duration-300 ease-out\",\n    style: _normalizeStyle({\n      width: `${$options.strengthInfo.score / $options.strengthInfo.maxScore * 100}%`,\n      backgroundColor: $options.strengthInfo.color\n    })\n  }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(4, i => {\n    return _createElementVNode(\"div\", {\n      key: i,\n      class: _normalizeClass([\"strength-segment\", {\n        'active': i <= Math.ceil($options.strengthInfo.score / $options.strengthInfo.maxScore * 4),\n        'very-weak': $options.strengthInfo.strength === 0,\n        'weak': $options.strengthInfo.strength === 1,\n        'medium': $options.strengthInfo.strength === 2,\n        'strong': $options.strengthInfo.strength === 3,\n        'very-strong': $options.strengthInfo.strength === 4\n      }])\n    }, null, 2 /* CLASS */);\n  }), 64 /* STABLE_FRAGMENT */))])]), _createCommentVNode(\" 强度文本和分数 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", {\n    class: \"text-sm font-medium transition-colors duration-200\",\n    style: _normalizeStyle({\n      color: $options.strengthInfo.color\n    })\n  }, _toDisplayString($options.strengthInfo.label), 5 /* TEXT, STYLE */), _createElementVNode(\"span\", _hoisted_6, _toDisplayString($options.strengthInfo.score) + \"/\" + _toDisplayString($options.strengthInfo.maxScore), 1 /* TEXT */)]), _createCommentVNode(\" 详细检查项 \"), $props.showDetails && $options.strengthInfo.checks ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.strengthInfo.checks, (check, key) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: key,\n      class: \"flex items-center text-xs\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', check ? 'check' : 'times'],\n      class: _normalizeClass([check ? 'text-green-500' : 'text-gray-400', \"mr-2 w-3 h-3\"])\n    }, null, 8 /* PROPS */, [\"icon\", \"class\"]), _createElementVNode(\"span\", {\n      class: _normalizeClass(check ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400')\n    }, _toDisplayString($options.getCheckLabel(key)), 3 /* TEXT, CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 改进建议 \"), $props.showSuggestions && $options.strengthInfo.feedback && $options.strengthInfo.feedback.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"text-xs text-gray-600 dark:text-gray-400 mb-1\"\n  }, \"改进建议：\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.strengthInfo.feedback, (suggestion, index) => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: index,\n      class: \"flex items-start text-gray-600 dark:text-gray-400\"\n    }, [_cache[0] || (_cache[0] = _createElementVNode(\"span\", {\n      class: \"mr-1\"\n    }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(suggestion), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "style", "_normalizeStyle", "$options", "strengthInfo", "score", "maxScore", "color", "_hoisted_4", "_Fragment", "_renderList", "i", "_normalizeClass", "Math", "ceil", "strength", "_hoisted_5", "label", "_hoisted_6", "_toDisplayString", "$props", "showDetails", "checks", "_hoisted_7", "check", "_createVNode", "_component_font_awesome_icon", "icon", "getCheckLabel", "showSuggestions", "feedback", "length", "_hoisted_8", "_hoisted_9", "suggestion", "index"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue"], "sourcesContent": ["<template>\r\n  <div class=\"password-strength-meter\">\r\n    <!-- 强度指示条 -->\r\n    <div class=\"strength-bar-container\">\r\n      <div class=\"strength-bar\">\r\n        <div\r\n          class=\"strength-fill transition-all duration-300 ease-out\"\r\n          :style=\"{\r\n            width: `${(strengthInfo.score / strengthInfo.maxScore) * 100}%`,\r\n            backgroundColor: strengthInfo.color\r\n          }\"\r\n        ></div>\r\n      </div>\r\n      <div class=\"strength-segments\">\r\n        <div\r\n          v-for=\"i in 4\"\r\n          :key=\"i\"\r\n          class=\"strength-segment\"\r\n          :class=\"{\r\n            'active': i <= Math.ceil((strengthInfo.score / strengthInfo.maxScore) * 4),\r\n            'very-weak': strengthInfo.strength === 0,\r\n            'weak': strengthInfo.strength === 1,\r\n            'medium': strengthInfo.strength === 2,\r\n            'strong': strengthInfo.strength === 3,\r\n            'very-strong': strengthInfo.strength === 4\r\n          }\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 强度文本和分数 -->\r\n    <div class=\"flex justify-between items-center mt-2\">\r\n      <span\r\n        class=\"text-sm font-medium transition-colors duration-200\"\r\n        :style=\"{ color: strengthInfo.color }\"\r\n      >\r\n        {{ strengthInfo.label }}\r\n      </span>\r\n      <span class=\"text-xs text-gray-500 dark:text-gray-400\">\r\n        {{ strengthInfo.score }}/{{ strengthInfo.maxScore }}\r\n      </span>\r\n    </div>\r\n\r\n    <!-- 详细检查项 -->\r\n    <div v-if=\"showDetails && strengthInfo.checks\" class=\"mt-3 space-y-1\">\r\n      <div\r\n        v-for=\"(check, key) in strengthInfo.checks\"\r\n        :key=\"key\"\r\n        class=\"flex items-center text-xs\"\r\n      >\r\n        <font-awesome-icon\r\n          :icon=\"['fas', check ? 'check' : 'times']\"\r\n          :class=\"check ? 'text-green-500' : 'text-gray-400'\"\r\n          class=\"mr-2 w-3 h-3\"\r\n        />\r\n        <span :class=\"check ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'\">\r\n          {{ getCheckLabel(key) }}\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 改进建议 -->\r\n    <div v-if=\"showSuggestions && strengthInfo.feedback && strengthInfo.feedback.length > 0\" class=\"mt-3\">\r\n      <div class=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">改进建议：</div>\r\n      <ul class=\"text-xs space-y-1\">\r\n        <li\r\n          v-for=\"(suggestion, index) in strengthInfo.feedback\"\r\n          :key=\"index\"\r\n          class=\"flex items-start text-gray-600 dark:text-gray-400\"\r\n        >\r\n          <span class=\"mr-1\">•</span>\r\n          <span>{{ suggestion }}</span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { checkPasswordStrength } from '@/utils/passwordUtils'\r\n\r\nexport default {\r\n  name: 'PasswordStrengthMeter',\r\n  props: {\r\n    password: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    showDetails: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showSuggestions: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    policy: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  computed: {\r\n    strengthInfo() {\r\n      return checkPasswordStrength(this.password)\r\n    }\r\n  },\r\n  methods: {\r\n    getCheckLabel(key) {\r\n      const labels = {\r\n        length: '长度足够',\r\n        lowercase: '包含小写字母',\r\n        uppercase: '包含大写字母',\r\n        numbers: '包含数字',\r\n        special: '包含特殊字符',\r\n        noCommon: '非常见密码'\r\n      }\r\n      return labels[key] || key\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.password-strength-meter {\r\n  @apply w-full;\r\n}\r\n\r\n.strength-bar-container {\r\n  @apply relative;\r\n}\r\n\r\n.strength-bar {\r\n  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;\r\n}\r\n\r\n.strength-fill {\r\n  @apply h-full rounded-full;\r\n}\r\n\r\n.strength-segments {\r\n  @apply absolute top-0 left-0 w-full h-full flex;\r\n}\r\n\r\n.strength-segment {\r\n  @apply flex-1 border-r border-white dark:border-gray-800 last:border-r-0;\r\n  @apply transition-all duration-300;\r\n}\r\n\r\n.strength-segment.active.very-weak {\r\n  @apply bg-red-500;\r\n}\r\n\r\n.strength-segment.active.weak {\r\n  @apply bg-orange-500;\r\n}\r\n\r\n.strength-segment.active.medium {\r\n  @apply bg-yellow-500;\r\n}\r\n\r\n.strength-segment.active.strong {\r\n  @apply bg-green-500;\r\n}\r\n\r\n.strength-segment.active.very-strong {\r\n  @apply bg-green-600;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n.strength-fill {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.strength-segment.active {\r\n  animation: pulse 2s infinite;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAc;;EASpBA,KAAK,EAAC;AAAmB;;EAkB3BA,KAAK,EAAC;AAAwC;;EAO3CA,KAAK,EAAC;AAA0C;;EAtC5DC,GAAA;EA4CmDD,KAAK,EAAC;;;EA5CzDC,GAAA;EA8D6FD,KAAK,EAAC;;;EAEzFA,KAAK,EAAC;AAAmB;;;uBA/DjCE,mBAAA,CA0EM,OA1ENC,UA0EM,GAzEJC,mBAAA,WAAc,EACdC,mBAAA,CAyBM,OAzBNC,UAyBM,GAxBJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAMO;IALLL,KAAK,EAAC,oDAAoD;IACzDQ,KAAK,EAPhBC,eAAA;gBAO4CC,QAAA,CAAAC,YAAY,CAACC,KAAK,GAAGF,QAAA,CAAAC,YAAY,CAACE,QAAQ;uBAA0CH,QAAA,CAAAC,YAAY,CAACG;;6BAMvIT,mBAAA,CAcM,OAdNU,UAcM,I,cAbJb,mBAAA,CAYOc,SAAA,QA1BfC,WAAA,CAesB,CAAC,EAANC,CAAC;WADVb,mBAAA,CAYO;MAVJJ,GAAG,EAAEiB,CAAC;MACPlB,KAAK,EAjBfmB,eAAA,EAiBgB,kBAAkB;kBACSD,CAAC,IAAIE,IAAI,CAACC,IAAI,CAAEX,QAAA,CAAAC,YAAY,CAACC,KAAK,GAAGF,QAAA,CAAAC,YAAY,CAACE,QAAQ;qBAAkCH,QAAA,CAAAC,YAAY,CAACW,QAAQ;gBAA6BZ,QAAA,CAAAC,YAAY,CAACW,QAAQ;kBAA+BZ,QAAA,CAAAC,YAAY,CAACW,QAAQ;kBAA+BZ,QAAA,CAAAC,YAAY,CAACW,QAAQ;uBAAoCZ,QAAA,CAAAC,YAAY,CAACW,QAAQ;;;sCAY3WlB,mBAAA,aAAgB,EAChBC,mBAAA,CAUM,OAVNkB,UAUM,GATJlB,mBAAA,CAKO;IAJLL,KAAK,EAAC,oDAAoD;IACzDQ,KAAK,EAlCdC,eAAA;MAAAK,KAAA,EAkCyBJ,QAAA,CAAAC,YAAY,CAACG;IAAK;sBAEhCJ,QAAA,CAAAC,YAAY,CAACa,KAAK,yBAEvBnB,mBAAA,CAEO,QAFPoB,UAEO,EAAAC,gBAAA,CADFhB,QAAA,CAAAC,YAAY,CAACC,KAAK,IAAG,GAAC,GAAAc,gBAAA,CAAGhB,QAAA,CAAAC,YAAY,CAACE,QAAQ,iB,GAIrDT,mBAAA,WAAc,EACHuB,MAAA,CAAAC,WAAW,IAAIlB,QAAA,CAAAC,YAAY,CAACkB,MAAM,I,cAA7C3B,mBAAA,CAeM,OAfN4B,UAeM,I,kBAdJ5B,mBAAA,CAaMc,SAAA,QA1DZC,WAAA,CA8C+BP,QAAA,CAAAC,YAAY,CAACkB,MAAM,EA9ClD,CA8CgBE,KAAK,EAAE9B,GAAG;yBADpBC,mBAAA,CAaM;MAXHD,GAAG,EAAEA,GAAG;MACTD,KAAK,EAAC;QAENgC,YAAA,CAIEC,4BAAA;MAHCC,IAAI,UAAUH,KAAK;MACnB/B,KAAK,EApDhBmB,eAAA,EAoDkBY,KAAK,uCACP,cAAc;gDAEtB1B,mBAAA,CAEO;MAFAL,KAAK,EAvDpBmB,eAAA,CAuDsBY,KAAK;wBACdrB,QAAA,CAAAyB,aAAa,CAAClC,GAAG,yB;sCAxD9BG,mBAAA,gBA6DIA,mBAAA,UAAa,EACFuB,MAAA,CAAAS,eAAe,IAAI1B,QAAA,CAAAC,YAAY,CAAC0B,QAAQ,IAAI3B,QAAA,CAAAC,YAAY,CAAC0B,QAAQ,CAACC,MAAM,Q,cAAnFpC,mBAAA,CAYM,OAZNqC,UAYM,G,0BAXJlC,mBAAA,CAAsE;IAAjEL,KAAK,EAAC;EAA+C,GAAC,OAAK,sBAChEK,mBAAA,CASK,MATLmC,UASK,I,kBARHtC,mBAAA,CAOKc,SAAA,QAxEbC,WAAA,CAkEwCP,QAAA,CAAAC,YAAY,CAAC0B,QAAQ,EAlE7D,CAkEkBI,UAAU,EAAEC,KAAK;yBAD3BxC,mBAAA,CAOK;MALFD,GAAG,EAAEyC,KAAK;MACX1C,KAAK,EAAC;kCAENK,mBAAA,CAA2B;MAArBL,KAAK,EAAC;IAAM,GAAC,GAAC,sBACpBK,mBAAA,CAA6B,cAAAqB,gBAAA,CAApBe,UAAU,iB;wCAvE7BrC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}