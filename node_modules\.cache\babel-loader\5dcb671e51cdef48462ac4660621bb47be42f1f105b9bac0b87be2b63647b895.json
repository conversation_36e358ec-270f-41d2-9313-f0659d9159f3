{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"password-strength-meter\"\n};\nconst _hoisted_2 = {\n  class: \"strength-bar-container\"\n};\nconst _hoisted_3 = {\n  class: \"strength-bar\"\n};\nconst _hoisted_4 = {\n  class: \"strength-segments\"\n};\nconst _hoisted_5 = {\n  class: \"flex justify-between items-center mt-2\"\n};\nconst _hoisted_6 = {\n  class: \"text-xs text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"mt-3 space-y-1\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"mt-3\"\n};\nconst _hoisted_9 = {\n  class: \"text-xs space-y-1\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 强度指示条 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", {\n    class: \"strength-fill transition-all duration-300 ease-out\",\n    style: _normalizeStyle({\n      width: `${_ctx.strengthInfo.score / _ctx.strengthInfo.maxScore * 100}%`,\n      backgroundColor: _ctx.strengthInfo.color\n    })\n  }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(4, i => {\n    return _createElementVNode(\"div\", {\n      key: i,\n      class: _normalizeClass([\"strength-segment\", {\n        'active': i <= Math.ceil(_ctx.strengthInfo.score / _ctx.strengthInfo.maxScore * 4),\n        'very-weak': _ctx.strengthInfo.strength === 0,\n        'weak': _ctx.strengthInfo.strength === 1,\n        'medium': _ctx.strengthInfo.strength === 2,\n        'strong': _ctx.strengthInfo.strength === 3,\n        'very-strong': _ctx.strengthInfo.strength === 4\n      }])\n    }, null, 2 /* CLASS */);\n  }), 64 /* STABLE_FRAGMENT */))])]), _createCommentVNode(\" 强度文本和分数 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", {\n    class: \"text-sm font-medium transition-colors duration-200\",\n    style: _normalizeStyle({\n      color: _ctx.strengthInfo.color\n    })\n  }, _toDisplayString(_ctx.strengthInfo.label), 5 /* TEXT, STYLE */), _createElementVNode(\"span\", _hoisted_6, _toDisplayString(_ctx.strengthInfo.score) + \"/\" + _toDisplayString(_ctx.strengthInfo.maxScore), 1 /* TEXT */)]), _createCommentVNode(\" 详细检查项 \"), _ctx.showDetails && _ctx.strengthInfo.checks ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.strengthInfo.checks, (check, key) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: key,\n      class: \"flex items-center text-xs\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', check ? 'check' : 'times'],\n      class: _normalizeClass([check ? 'text-green-500' : 'text-gray-400', \"mr-2 w-3 h-3\"])\n    }, null, 8 /* PROPS */, [\"icon\", \"class\"]), _createElementVNode(\"span\", {\n      class: _normalizeClass(check ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400')\n    }, _toDisplayString(_ctx.getCheckLabel(key)), 3 /* TEXT, CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 改进建议 \"), _ctx.showSuggestions && _ctx.strengthInfo.feedback && _ctx.strengthInfo.feedback.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"text-xs text-gray-600 dark:text-gray-400 mb-1\"\n  }, \"改进建议：\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.strengthInfo.feedback, (suggestion, index) => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: index,\n      class: \"flex items-start text-gray-600 dark:text-gray-400\"\n    }, [_cache[0] || (_cache[0] = _createElementVNode(\"span\", {\n      class: \"mr-1\"\n    }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(suggestion), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "style", "_normalizeStyle", "_ctx", "strengthInfo", "score", "maxScore", "color", "_hoisted_4", "_Fragment", "_renderList", "i", "_normalizeClass", "Math", "ceil", "strength", "_hoisted_5", "label", "_hoisted_6", "_toDisplayString", "showDetails", "checks", "_hoisted_7", "check", "_createVNode", "_component_font_awesome_icon", "icon", "getCheckLabel", "showSuggestions", "feedback", "length", "_hoisted_8", "_hoisted_9", "suggestion", "index"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue"], "sourcesContent": ["<template>\r\n  <div class=\"password-strength-meter\">\r\n    <!-- 强度指示条 -->\r\n    <div class=\"strength-bar-container\">\r\n      <div class=\"strength-bar\">\r\n        <div\r\n          class=\"strength-fill transition-all duration-300 ease-out\"\r\n          :style=\"{\r\n            width: `${(strengthInfo.score / strengthInfo.maxScore) * 100}%`,\r\n            backgroundColor: strengthInfo.color\r\n          }\"\r\n        ></div>\r\n      </div>\r\n      <div class=\"strength-segments\">\r\n        <div\r\n          v-for=\"i in 4\"\r\n          :key=\"i\"\r\n          class=\"strength-segment\"\r\n          :class=\"{\r\n            'active': i <= Math.ceil((strengthInfo.score / strengthInfo.maxScore) * 4),\r\n            'very-weak': strengthInfo.strength === 0,\r\n            'weak': strengthInfo.strength === 1,\r\n            'medium': strengthInfo.strength === 2,\r\n            'strong': strengthInfo.strength === 3,\r\n            'very-strong': strengthInfo.strength === 4\r\n          }\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 强度文本和分数 -->\r\n    <div class=\"flex justify-between items-center mt-2\">\r\n      <span\r\n        class=\"text-sm font-medium transition-colors duration-200\"\r\n        :style=\"{ color: strengthInfo.color }\"\r\n      >\r\n        {{ strengthInfo.label }}\r\n      </span>\r\n      <span class=\"text-xs text-gray-500 dark:text-gray-400\">\r\n        {{ strengthInfo.score }}/{{ strengthInfo.maxScore }}\r\n      </span>\r\n    </div>\r\n\r\n    <!-- 详细检查项 -->\r\n    <div v-if=\"showDetails && strengthInfo.checks\" class=\"mt-3 space-y-1\">\r\n      <div\r\n        v-for=\"(check, key) in strengthInfo.checks\"\r\n        :key=\"key\"\r\n        class=\"flex items-center text-xs\"\r\n      >\r\n        <font-awesome-icon\r\n          :icon=\"['fas', check ? 'check' : 'times']\"\r\n          :class=\"check ? 'text-green-500' : 'text-gray-400'\"\r\n          class=\"mr-2 w-3 h-3\"\r\n        />\r\n        <span :class=\"check ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'\">\r\n          {{ getCheckLabel(key) }}\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 改进建议 -->\r\n    <div v-if=\"showSuggestions && strengthInfo.feedback && strengthInfo.feedback.length > 0\" class=\"mt-3\">\r\n      <div class=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">改进建议：</div>\r\n      <ul class=\"text-xs space-y-1\">\r\n        <li\r\n          v-for=\"(suggestion, index) in strengthInfo.feedback\"\r\n          :key=\"index\"\r\n          class=\"flex items-start text-gray-600 dark:text-gray-400\"\r\n        >\r\n          <span class=\"mr-1\">•</span>\r\n          <span>{{ suggestion }}</span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PasswordStrengthMeter',\r\n  props: {\r\n    password: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    score() {\r\n      if (!this.password) return 0\r\n      \r\n      // 简单评分算法，实际应用中可使用更复杂的算法\r\n      const length = this.password.length\r\n      const hasLowerCase = /[a-z]/.test(this.password)\r\n      const hasUpperCase = /[A-Z]/.test(this.password)\r\n      const hasNumbers = /\\d/.test(this.password)\r\n      const hasSpecialChars = /[^A-Za-z0-9]/.test(this.password)\r\n      \r\n      let score = 0\r\n      \r\n      if (length >= 8) score += 1\r\n      if (length >= 12) score += 1\r\n      if (hasLowerCase && hasUpperCase) score += 1\r\n      if (hasNumbers) score += 1\r\n      if (hasSpecialChars) score += 1\r\n      \r\n      // 最高分为4\r\n      return Math.min(4, Math.floor(score / 1.25))\r\n    },\r\n    \r\n    strengthText() {\r\n      switch (this.score) {\r\n        case 0: return '请输入密码'\r\n        case 1: return '弱'\r\n        case 2: return '中等'\r\n        case 3: return '强'\r\n        case 4: return '非常强'\r\n        default: return ''\r\n      }\r\n    },\r\n    \r\n    strengthTextClass() {\r\n      switch (this.score) {\r\n        case 0: return 'text-gray-500'\r\n        case 1: return 'text-red-600'\r\n        case 2: return 'text-yellow-600'\r\n        case 3:\r\n        case 4: return 'text-green-600'\r\n        default: return ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAc;;EASpBA,KAAK,EAAC;AAAmB;;EAkB3BA,KAAK,EAAC;AAAwC;;EAO3CA,KAAK,EAAC;AAA0C;;EAtC5DC,GAAA;EA4CmDD,KAAK,EAAC;;;EA5CzDC,GAAA;EA8D6FD,KAAK,EAAC;;;EAEzFA,KAAK,EAAC;AAAmB;;;uBA/DjCE,mBAAA,CA0EM,OA1ENC,UA0EM,GAzEJC,mBAAA,WAAc,EACdC,mBAAA,CAyBM,OAzBNC,UAyBM,GAxBJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAMO;IALLL,KAAK,EAAC,oDAAoD;IACzDQ,KAAK,EAPhBC,eAAA;gBAO4CC,IAAA,CAAAC,YAAY,CAACC,KAAK,GAAGF,IAAA,CAAAC,YAAY,CAACE,QAAQ;uBAA0CH,IAAA,CAAAC,YAAY,CAACG;;6BAMvIT,mBAAA,CAcM,OAdNU,UAcM,I,cAbJb,mBAAA,CAYOc,SAAA,QA1BfC,WAAA,CAesB,CAAC,EAANC,CAAC;WADVb,mBAAA,CAYO;MAVJJ,GAAG,EAAEiB,CAAC;MACPlB,KAAK,EAjBfmB,eAAA,EAiBgB,kBAAkB;kBACSD,CAAC,IAAIE,IAAI,CAACC,IAAI,CAAEX,IAAA,CAAAC,YAAY,CAACC,KAAK,GAAGF,IAAA,CAAAC,YAAY,CAACE,QAAQ;qBAAkCH,IAAA,CAAAC,YAAY,CAACW,QAAQ;gBAA6BZ,IAAA,CAAAC,YAAY,CAACW,QAAQ;kBAA+BZ,IAAA,CAAAC,YAAY,CAACW,QAAQ;kBAA+BZ,IAAA,CAAAC,YAAY,CAACW,QAAQ;uBAAoCZ,IAAA,CAAAC,YAAY,CAACW,QAAQ;;;sCAY3WlB,mBAAA,aAAgB,EAChBC,mBAAA,CAUM,OAVNkB,UAUM,GATJlB,mBAAA,CAKO;IAJLL,KAAK,EAAC,oDAAoD;IACzDQ,KAAK,EAlCdC,eAAA;MAAAK,KAAA,EAkCyBJ,IAAA,CAAAC,YAAY,CAACG;IAAK;sBAEhCJ,IAAA,CAAAC,YAAY,CAACa,KAAK,yBAEvBnB,mBAAA,CAEO,QAFPoB,UAEO,EAAAC,gBAAA,CADFhB,IAAA,CAAAC,YAAY,CAACC,KAAK,IAAG,GAAC,GAAAc,gBAAA,CAAGhB,IAAA,CAAAC,YAAY,CAACE,QAAQ,iB,GAIrDT,mBAAA,WAAc,EACHM,IAAA,CAAAiB,WAAW,IAAIjB,IAAA,CAAAC,YAAY,CAACiB,MAAM,I,cAA7C1B,mBAAA,CAeM,OAfN2B,UAeM,I,kBAdJ3B,mBAAA,CAaMc,SAAA,QA1DZC,WAAA,CA8C+BP,IAAA,CAAAC,YAAY,CAACiB,MAAM,EA9ClD,CA8CgBE,KAAK,EAAE7B,GAAG;yBADpBC,mBAAA,CAaM;MAXHD,GAAG,EAAEA,GAAG;MACTD,KAAK,EAAC;QAEN+B,YAAA,CAIEC,4BAAA;MAHCC,IAAI,UAAUH,KAAK;MACnB9B,KAAK,EApDhBmB,eAAA,EAoDkBW,KAAK,uCACP,cAAc;gDAEtBzB,mBAAA,CAEO;MAFAL,KAAK,EAvDpBmB,eAAA,CAuDsBW,KAAK;wBACdpB,IAAA,CAAAwB,aAAa,CAACjC,GAAG,yB;sCAxD9BG,mBAAA,gBA6DIA,mBAAA,UAAa,EACFM,IAAA,CAAAyB,eAAe,IAAIzB,IAAA,CAAAC,YAAY,CAACyB,QAAQ,IAAI1B,IAAA,CAAAC,YAAY,CAACyB,QAAQ,CAACC,MAAM,Q,cAAnFnC,mBAAA,CAYM,OAZNoC,UAYM,G,0BAXJjC,mBAAA,CAAsE;IAAjEL,KAAK,EAAC;EAA+C,GAAC,OAAK,sBAChEK,mBAAA,CASK,MATLkC,UASK,I,kBARHrC,mBAAA,CAOKc,SAAA,QAxEbC,WAAA,CAkEwCP,IAAA,CAAAC,YAAY,CAACyB,QAAQ,EAlE7D,CAkEkBI,UAAU,EAAEC,KAAK;yBAD3BvC,mBAAA,CAOK;MALFD,GAAG,EAAEwC,KAAK;MACXzC,KAAK,EAAC;kCAENK,mBAAA,CAA2B;MAArBL,KAAK,EAAC;IAAM,GAAC,GAAC,sBACpBK,mBAAA,CAA6B,cAAAqB,gBAAA,CAApBc,UAAU,iB;wCAvE7BpC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}