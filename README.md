# 密码管理系统

一个现代化的企业级密码管理系统，专为运维人员设计，提供安全、高效、易用的密码管理解决方案。

## ✨ 核心优势

- **🔒 企业级安全**：多层加密、权限控制、审计追踪
- **🚀 智能自动化**：自动密码轮换、智能风险评估
- **💡 用户友好**：直观界面、批量操作、实时反馈
- **🛡️ 合规保障**：符合行业标准、完整审计日志

## 功能特性

- **主机密码管理**：集中管理服务器和主机账号密码
- **密码策略管理**：支持多种安全策略，适应不同安全需求
- **定时更新任务**：支持自动化密码轮换，确保密码安全
- **紧急重置功能**：应对安全事件的紧急密码处理机制
- **批量操作支持**：高效管理多台主机密码

## 技术栈

- **前端框架**：Vue.js 3
- **状态管理**：Vuex 4
- **路由管理**：Vue Router 4
- **样式方案**：Tailwind CSS
- **图标方案**：Font Awesome

## 快速开始

### 前置条件

- Node.js (>= 14.x)
- npm 或 yarn

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/yourname/password-management.git
cd password-management
```

2. 安装依赖
```bash
npm install
# 或
yarn install
```

3. 启动开发服务器
```bash
npm run serve
# 或
yarn serve
```

4. 构建生产版本
```bash
npm run build
# 或
yarn build
```

## 项目结构

```
src/
├── assets/          # 静态资源文件
│   └── styles/      # 全局样式
├── components/      # 通用组件
├── router/          # 路由配置
├── store/           # Vuex状态管理
├── utils/           # 工具类
├── views/           # 页面组件
├── App.vue          # 根组件
└── main.js          # 应用入口
```

## 核心模块

### 主机管理

提供主机列表、状态监控和密码更新功能，支持单台主机操作和批量操作。

### 密码策略

管理多种密码策略，支持配置密码复杂度、有效期、轮换规则等。

### 定时任务

创建和管理自动化密码更新任务，支持多种调度选项和执行计划。

## 安全特性

- 密码强度实时评估
- 密码策略合规性检查
- 密码历史记录管理
- 操作审计日志

## 环境配置

系统支持多环境部署，可通过环境变量或配置文件自定义：

- 开发环境 (development)
- 测试环境 (test)
- 生产环境 (production)

## 🎉 最新改进 (v2.0)

### 全新UI设计
- **现代化界面**：采用最新的设计语言，提供更加直观美观的用户体验
- **暗色主题**：支持浅色/暗色主题切换，适应不同使用环境
- **响应式设计**：完美适配桌面端和移动端设备

### 新增功能模块
- **安全概览仪表板**：全面的安全状态监控和风险分析
- **高级密码生成器**：支持多种强度和类型的智能密码生成
- **实时通知系统**：重要事件的实时通知和操作提醒
- **智能安全建议**：基于分析结果的自动化安全改进建议

### 交互体验优化
- **平滑动画**：优雅的页面过渡和交互动画效果
- **智能筛选**：多维度的搜索和筛选功能
- **批量操作**：更加高效的批量操作界面
- **键盘导航**：完整的键盘快捷键支持

### 运维友好特性
- **双视图模式**：表格视图和卡片视图自由切换
- **风险预警**：自动化的安全风险预警系统
- **操作审计**：完整的操作日志和审计追踪
- **合规检查**：密码策略的合规性检查和报告

详细的改进说明请查看 [IMPROVEMENTS.md](./IMPROVEMENTS.md) 文件。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

