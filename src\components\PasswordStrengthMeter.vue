<template>
  <div class="password-strength-meter">
    <!-- 强度指示条 -->
    <div class="strength-bar-container">
      <div class="strength-bar">
        <div
          class="strength-fill transition-all duration-300 ease-out"
          :style="{
            width: `${(strengthInfo.score / strengthInfo.maxScore) * 100}%`,
            backgroundColor: strengthInfo.color
          }"
        ></div>
      </div>
      <div class="strength-segments">
        <div
          v-for="i in 4"
          :key="i"
          class="strength-segment"
          :class="{
            'active': i <= Math.ceil((strengthInfo.score / strengthInfo.maxScore) * 4),
            'very-weak': strengthInfo.strength === 0,
            'weak': strengthInfo.strength === 1,
            'medium': strengthInfo.strength === 2,
            'strong': strengthInfo.strength === 3,
            'very-strong': strengthInfo.strength === 4
          }"
        ></div>
      </div>
    </div>

    <!-- 强度文本和分数 -->
    <div class="flex justify-between items-center mt-2">
      <span
        class="text-sm font-medium transition-colors duration-200"
        :style="{ color: strengthInfo.color }"
      >
        {{ strengthInfo.label }}
      </span>
      <span class="text-xs text-gray-500 dark:text-gray-400">
        {{ strengthInfo.score }}/{{ strengthInfo.maxScore }}
      </span>
    </div>

    <!-- 详细检查项 -->
    <div v-if="showDetails && strengthInfo.checks" class="mt-3 space-y-1">
      <div
        v-for="(check, key) in strengthInfo.checks"
        :key="key"
        class="flex items-center text-xs"
      >
        <font-awesome-icon
          :icon="['fas', check ? 'check' : 'times']"
          :class="check ? 'text-green-500' : 'text-gray-400'"
          class="mr-2 w-3 h-3"
        />
        <span :class="check ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'">
          {{ getCheckLabel(key) }}
        </span>
      </div>
    </div>

    <!-- 改进建议 -->
    <div v-if="showSuggestions && strengthInfo.feedback && strengthInfo.feedback.length > 0" class="mt-3">
      <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">改进建议：</div>
      <ul class="text-xs space-y-1">
        <li
          v-for="(suggestion, index) in strengthInfo.feedback"
          :key="index"
          class="flex items-start text-gray-600 dark:text-gray-400"
        >
          <span class="mr-1">•</span>
          <span>{{ suggestion }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { checkPasswordStrength } from '@/utils/passwordUtils'

export default {
  name: 'PasswordStrengthMeter',
  props: {
    password: {
      type: String,
      default: ''
    },
    showDetails: {
      type: Boolean,
      default: false
    },
    showSuggestions: {
      type: Boolean,
      default: false
    },
    policy: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    strengthInfo() {
      return checkPasswordStrength(this.password)
    }
  },
  methods: {
    getCheckLabel(key) {
      const labels = {
        length: '长度足够',
        lowercase: '包含小写字母',
        uppercase: '包含大写字母',
        numbers: '包含数字',
        special: '包含特殊字符',
        noCommon: '非常见密码'
      }
      return labels[key] || key
    }
  }
}
</script>

<style scoped>
.password-strength-meter {
  @apply w-full;
}

.strength-bar-container {
  @apply relative;
}

.strength-bar {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.strength-fill {
  @apply h-full rounded-full;
}

.strength-segments {
  @apply absolute top-0 left-0 w-full h-full flex;
}

.strength-segment {
  @apply flex-1 border-r border-white dark:border-gray-800 last:border-r-0;
  @apply transition-all duration-300;
}

.strength-segment.active.very-weak {
  @apply bg-red-500;
}

.strength-segment.active.weak {
  @apply bg-orange-500;
}

.strength-segment.active.medium {
  @apply bg-yellow-500;
}

.strength-segment.active.strong {
  @apply bg-green-500;
}

.strength-segment.active.very-strong {
  @apply bg-green-600;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.strength-fill {
  animation: pulse 2s infinite;
}

.strength-segment.active {
  animation: pulse 2s infinite;
}
</style>