/**
 * 数据验证工具类
 * 提供各种数据验证功能
 */

/**
 * 验证规则类型
 */
export const ValidationRules = {
  REQUIRED: 'required',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  PATTERN: 'pattern',
  EMAIL: 'email',
  IP: 'ip',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  POSITIVE: 'positive',
  RANGE: 'range',
  CUSTOM: 'custom'
}

/**
 * 验证器类
 */
export class Validator {
  constructor() {
    this.rules = new Map()
    this.setupDefaultRules()
  }

  /**
   * 设置默认验证规则
   */
  setupDefaultRules() {
    // 必填验证
    this.addRule(ValidationRules.REQUIRED, (value) => {
      if (value === null || value === undefined || value === '') {
        return '此字段为必填项'
      }
      return null
    })

    // 最小长度验证
    this.addRule(ValidationRules.MIN_LENGTH, (value, minLength) => {
      if (value && value.length < minLength) {
        return `最少需要${minLength}个字符`
      }
      return null
    })

    // 最大长度验证
    this.addRule(ValidationRules.MAX_LENGTH, (value, maxLength) => {
      if (value && value.length > maxLength) {
        return `最多允许${maxLength}个字符`
      }
      return null
    })

    // 正则表达式验证
    this.addRule(ValidationRules.PATTERN, (value, pattern, message) => {
      if (value && !pattern.test(value)) {
        return message || '格式不正确'
      }
      return null
    })

    // 邮箱验证
    this.addRule(ValidationRules.EMAIL, (value) => {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (value && !emailPattern.test(value)) {
        return '请输入有效的邮箱地址'
      }
      return null
    })

    // IP地址验证
    this.addRule(ValidationRules.IP, (value) => {
      const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
      if (value && !ipPattern.test(value)) {
        return '请输入有效的IP地址'
      }
      return null
    })

    // URL验证
    this.addRule(ValidationRules.URL, (value) => {
      try {
        if (value) {
          new URL(value)
        }
        return null
      } catch {
        return '请输入有效的URL地址'
      }
    })

    // 数字验证
    this.addRule(ValidationRules.NUMBER, (value) => {
      if (value && isNaN(Number(value))) {
        return '请输入有效的数字'
      }
      return null
    })

    // 整数验证
    this.addRule(ValidationRules.INTEGER, (value) => {
      if (value && (!Number.isInteger(Number(value)))) {
        return '请输入有效的整数'
      }
      return null
    })

    // 正数验证
    this.addRule(ValidationRules.POSITIVE, (value) => {
      if (value && Number(value) <= 0) {
        return '请输入正数'
      }
      return null
    })

    // 范围验证
    this.addRule(ValidationRules.RANGE, (value, min, max) => {
      const num = Number(value)
      if (value && (num < min || num > max)) {
        return `请输入${min}到${max}之间的数值`
      }
      return null
    })
  }

  /**
   * 添加验证规则
   * @param {string} name 规则名称
   * @param {Function} validator 验证函数
   */
  addRule(name, validator) {
    this.rules.set(name, validator)
  }

  /**
   * 验证单个值
   * @param {any} value 要验证的值
   * @param {Array} rules 验证规则数组
   * @returns {Array} 错误消息数组
   */
  validate(value, rules = []) {
    const errors = []

    for (const rule of rules) {
      let ruleName, ruleParams, customMessage

      if (typeof rule === 'string') {
        ruleName = rule
        ruleParams = []
      } else if (Array.isArray(rule)) {
        [ruleName, ...ruleParams] = rule
      } else if (typeof rule === 'object') {
        ruleName = rule.rule
        ruleParams = rule.params || []
        customMessage = rule.message
      }

      const validator = this.rules.get(ruleName)
      if (!validator) {
        console.warn(`Unknown validation rule: ${ruleName}`)
        continue
      }

      const error = validator(value, ...ruleParams)
      if (error) {
        errors.push(customMessage || error)
      }
    }

    return errors
  }

  /**
   * 验证对象
   * @param {Object} data 要验证的数据对象
   * @param {Object} schema 验证模式
   * @returns {Object} 验证结果
   */
  validateObject(data, schema) {
    const errors = {}
    let isValid = true

    for (const [field, rules] of Object.entries(schema)) {
      const fieldErrors = this.validate(data[field], rules)
      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors
        isValid = false
      }
    }

    return {
      isValid,
      errors,
      data
    }
  }
}

// 创建默认验证器实例
export const validator = new Validator()

/**
 * 便捷的验证函数
 */
export function validateField(value, rules) {
  return validator.validate(value, rules)
}

export function validateForm(data, schema) {
  return validator.validateObject(data, schema)
}

/**
 * 常用验证模式
 */
export const ValidationSchemas = {
  // 主机信息验证
  host: {
    name: [
      ValidationRules.REQUIRED,
      [ValidationRules.MIN_LENGTH, 1],
      [ValidationRules.MAX_LENGTH, 50],
      [ValidationRules.PATTERN, /^[a-zA-Z0-9\-_.]+$/, '主机名只能包含字母、数字、连字符、下划线和点']
    ],
    ip: [
      ValidationRules.REQUIRED,
      ValidationRules.IP
    ]
  },

  // 账号信息验证
  account: {
    username: [
      ValidationRules.REQUIRED,
      [ValidationRules.MIN_LENGTH, 2],
      [ValidationRules.MAX_LENGTH, 32],
      [ValidationRules.PATTERN, /^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线']
    ],
    password: [
      ValidationRules.REQUIRED,
      [ValidationRules.MIN_LENGTH, 8]
    ]
  },

  // 密码策略验证
  policy: {
    name: [
      ValidationRules.REQUIRED,
      [ValidationRules.MIN_LENGTH, 2],
      [ValidationRules.MAX_LENGTH, 50]
    ],
    minLength: [
      ValidationRules.REQUIRED,
      ValidationRules.INTEGER,
      ValidationRules.POSITIVE,
      [ValidationRules.RANGE, 6, 128]
    ],
    expiryDays: [
      ValidationRules.REQUIRED,
      ValidationRules.INTEGER,
      ValidationRules.POSITIVE,
      [ValidationRules.RANGE, 1, 365]
    ],
    historyCount: [
      ValidationRules.INTEGER,
      ValidationRules.POSITIVE,
      [ValidationRules.RANGE, 1, 50]
    ]
  },

  // 定时任务验证
  task: {
    name: [
      ValidationRules.REQUIRED,
      [ValidationRules.MIN_LENGTH, 2],
      [ValidationRules.MAX_LENGTH, 100]
    ],
    target: [
      ValidationRules.REQUIRED,
      [ValidationRules.MIN_LENGTH, 2]
    ],
    schedule: [
      ValidationRules.REQUIRED
    ]
  }
}

/**
 * 特殊验证函数
 */

/**
 * 验证密码强度
 * @param {string} password 密码
 * @param {Object} policy 密码策略
 * @returns {Array} 错误消息数组
 */
export function validatePasswordStrength(password, policy = {}) {
  const errors = []

  if (!password) {
    errors.push('密码不能为空')
    return errors
  }

  // 长度检查
  if (policy.minLength && password.length < policy.minLength) {
    errors.push(`密码长度不能少于${policy.minLength}位`)
  }

  // 字符要求检查
  if (policy.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母')
  }

  if (policy.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母')
  }

  if (policy.requireNumbers && !/[0-9]/.test(password)) {
    errors.push('密码必须包含数字')
  }

  if (policy.requireSpecial && !/[^a-zA-Z0-9]/.test(password)) {
    errors.push('密码必须包含特殊字符')
  }

  return errors
}

/**
 * 验证Cron表达式
 * @param {string} cronExpression Cron表达式
 * @returns {Array} 错误消息数组
 */
export function validateCronExpression(cronExpression) {
  const errors = []

  if (!cronExpression) {
    errors.push('Cron表达式不能为空')
    return errors
  }

  // 简单的Cron表达式验证
  const parts = cronExpression.trim().split(/\s+/)
  if (parts.length !== 5 && parts.length !== 6) {
    errors.push('Cron表达式格式不正确')
  }

  return errors
}

/**
 * 验证JSON格式
 * @param {string} jsonString JSON字符串
 * @returns {Array} 错误消息数组
 */
export function validateJSON(jsonString) {
  const errors = []

  if (!jsonString) {
    errors.push('JSON不能为空')
    return errors
  }

  try {
    JSON.parse(jsonString)
  } catch (e) {
    errors.push('JSON格式不正确')
  }

  return errors
}

export default validator
