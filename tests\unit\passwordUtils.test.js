/**
 * 密码工具类单元测试
 */

import {
  checkPasswordStrength,
  generateSecurePassword,
  validatePasswordPolicy,
  calculatePasswordExpiry,
  formatPasswordDisplay,
  isWeakPassword,
  updatePasswordHistory,
  isPasswordInHistory,
  generatePasswordComplexityReport
} from '@/utils/passwordUtils'

describe('passwordUtils', () => {
  describe('checkPasswordStrength', () => {
    test('应该正确评估空密码', () => {
      const result = checkPasswordStrength('')
      expect(result.strength).toBe(0)
      expect(result.score).toBe(0)
      expect(result.feedback).toContain('密码不能为空')
    })

    test('应该正确评估弱密码', () => {
      const result = checkPasswordStrength('123456')
      expect(result.strength).toBeLessThan(2)
      expect(result.feedback.length).toBeGreaterThan(0)
    })

    test('应该正确评估强密码', () => {
      const result = checkPasswordStrength('MyStr0ng!P@ssw0rd')
      expect(result.strength).toBeGreaterThanOrEqual(3)
      expect(result.checks.length).toBeTruthy()
      expect(result.checks.uppercase).toBe(true)
      expect(result.checks.lowercase).toBe(true)
      expect(result.checks.numbers).toBe(true)
      expect(result.checks.special).toBe(true)
    })

    test('应该检测常见密码', () => {
      const result = checkPasswordStrength('password123')
      expect(result.checks.noCommon).toBe(false)
      expect(result.feedback).toContain('避免使用常见密码')
    })
  })

  describe('generateSecurePassword', () => {
    test('应该生成指定长度的密码', () => {
      const password = generateSecurePassword({ length: 16 })
      expect(password).toHaveLength(16)
    })

    test('应该根据选项生成密码', () => {
      const password = generateSecurePassword({
        length: 12,
        includeUppercase: true,
        includeLowercase: true,
        includeNumbers: true,
        includeSpecial: false
      })
      
      expect(password).toHaveLength(12)
      expect(/[A-Z]/.test(password)).toBe(true)
      expect(/[a-z]/.test(password)).toBe(true)
      expect(/[0-9]/.test(password)).toBe(true)
      expect(/[^a-zA-Z0-9]/.test(password)).toBe(false)
    })

    test('应该排除相似字符', () => {
      const password = generateSecurePassword({
        length: 20,
        excludeSimilar: true
      })
      
      expect(/[0Ol1I]/.test(password)).toBe(false)
    })

    test('应该抛出错误当字符集为空时', () => {
      expect(() => {
        generateSecurePassword({
          includeUppercase: false,
          includeLowercase: false,
          includeNumbers: false,
          includeSpecial: false
        })
      }).toThrow('字符集不能为空')
    })
  })

  describe('validatePasswordPolicy', () => {
    const policy = {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecial: true,
      forbidUsername: true,
      username: 'testuser'
    }

    test('应该验证符合策略的密码', () => {
      const result = validatePasswordPolicy('MyStr0ng!P@ss', policy)
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('应该检测长度不足', () => {
      const result = validatePasswordPolicy('Short1!', policy)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('密码长度不能少于8位')
    })

    test('应该检测缺少字符类型', () => {
      const result = validatePasswordPolicy('onlylowercase', policy)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('密码必须包含大写字母')
      expect(result.errors).toContain('密码必须包含数字')
      expect(result.errors).toContain('密码必须包含特殊字符')
    })

    test('应该检测包含用户名', () => {
      const result = validatePasswordPolicy('testuser123!', policy)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('密码不能包含用户名')
    })
  })

  describe('calculatePasswordExpiry', () => {
    test('应该正确计算未过期密码', () => {
      const lastChange = new Date()
      const result = calculatePasswordExpiry(lastChange, 30)
      
      expect(result.daysRemaining).toBeGreaterThan(25)
      expect(result.status).toBe('normal')
      expect(result.isExpired).toBe(false)
    })

    test('应该正确检测过期密码', () => {
      const lastChange = new Date(Date.now() - 40 * 24 * 60 * 60 * 1000) // 40天前
      const result = calculatePasswordExpiry(lastChange, 30)
      
      expect(result.daysRemaining).toBeLessThan(0)
      expect(result.status).toBe('expired')
      expect(result.isExpired).toBe(true)
    })

    test('应该正确检测即将过期的密码', () => {
      const lastChange = new Date(Date.now() - 25 * 24 * 60 * 60 * 1000) // 25天前
      const result = calculatePasswordExpiry(lastChange, 30)
      
      expect(result.daysRemaining).toBeLessThanOrEqual(7)
      expect(result.isExpiringSoon).toBe(true)
    })
  })

  describe('formatPasswordDisplay', () => {
    test('应该遮罩密码', () => {
      const result = formatPasswordDisplay('password123', true)
      expect(result).toBe('••••••••••••')
    })

    test('应该显示明文密码', () => {
      const result = formatPasswordDisplay('password123', false)
      expect(result).toBe('password123')
    })

    test('应该处理空密码', () => {
      const result = formatPasswordDisplay('', true)
      expect(result).toBe('')
    })
  })

  describe('isWeakPassword', () => {
    test('应该检测弱密码', () => {
      expect(isWeakPassword('123456')).toBe(true)
      expect(isWeakPassword('password')).toBe(true)
      expect(isWeakPassword('admin')).toBe(true)
      expect(isWeakPassword('aaaa')).toBe(true)
      expect(isWeakPassword('short')).toBe(true)
    })

    test('应该识别强密码', () => {
      expect(isWeakPassword('MyStr0ng!P@ssw0rd')).toBe(false)
      expect(isWeakPassword('C0mpl3x!P@ss')).toBe(false)
    })
  })

  describe('updatePasswordHistory', () => {
    test('应该添加新密码到历史记录', () => {
      const history = []
      const newHistory = updatePasswordHistory('newpassword', history, 5)
      
      expect(newHistory).toHaveLength(1)
      expect(newHistory[0].password).toBe('newpassword')
      expect(newHistory[0].timestamp).toBeDefined()
    })

    test('应该限制历史记录数量', () => {
      const history = [
        { password: 'old1', timestamp: new Date(), id: 1 },
        { password: 'old2', timestamp: new Date(), id: 2 },
        { password: 'old3', timestamp: new Date(), id: 3 }
      ]
      
      const newHistory = updatePasswordHistory('new', history, 3)
      expect(newHistory).toHaveLength(3)
      expect(newHistory[0].password).toBe('new')
    })
  })

  describe('isPasswordInHistory', () => {
    const history = [
      { password: 'old1', timestamp: new Date(), id: 1 },
      { password: 'old2', timestamp: new Date(), id: 2 }
    ]

    test('应该检测密码是否在历史记录中', () => {
      expect(isPasswordInHistory('old1', history)).toBe(true)
      expect(isPasswordInHistory('new', history)).toBe(false)
    })

    test('应该处理空历史记录', () => {
      expect(isPasswordInHistory('password', [])).toBe(false)
    })
  })

  describe('generatePasswordComplexityReport', () => {
    test('应该生成复杂度报告', () => {
      const report = generatePasswordComplexityReport('MyStr0ng!P@ssw0rd')
      
      expect(report.score).toBeGreaterThan(0)
      expect(report.maxScore).toBe(100)
      expect(report.details).toHaveProperty('length')
      expect(report.details).toHaveProperty('variety')
      expect(report.details).toHaveProperty('uniqueness')
      expect(report.details).toHaveProperty('patterns')
    })

    test('应该处理空密码', () => {
      const report = generatePasswordComplexityReport('')
      
      expect(report.score).toBe(0)
      expect(report.maxScore).toBe(100)
    })

    test('应该给强密码高分', () => {
      const report = generatePasswordComplexityReport('MyVeryStr0ng!P@ssw0rd2023')
      
      expect(report.score).toBeGreaterThan(80)
    })
  })
})
