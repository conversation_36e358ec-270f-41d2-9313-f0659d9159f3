/**
 * 错误处理工具类
 * 提供统一的错误处理、日志记录和用户友好的错误提示
 */

import { showError, showWarning } from './notification.js'

/**
 * 错误类型枚举
 */
export const ErrorTypes = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown'
}

/**
 * 错误级别枚举
 */
export const ErrorLevels = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  constructor() {
    this.errorLog = []
    this.maxLogSize = 100
    this.setupGlobalErrorHandlers()
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, ErrorTypes.UNKNOWN, ErrorLevels.HIGH)
      event.preventDefault()
    })

    // 捕获全局JavaScript错误
    window.addEventListener('error', (event) => {
      this.handleError(event.error, ErrorTypes.CLIENT, ErrorLevels.MEDIUM)
    })
  }

  /**
   * 处理错误
   * @param {Error|string} error 错误对象或错误消息
   * @param {string} type 错误类型
   * @param {string} level 错误级别
   * @param {Object} context 错误上下文
   */
  handleError(error, type = ErrorTypes.UNKNOWN, level = ErrorLevels.MEDIUM, context = {}) {
    const errorInfo = this.normalizeError(error, type, level, context)
    
    // 记录错误日志
    this.logError(errorInfo)
    
    // 显示用户友好的错误提示
    this.showUserError(errorInfo)
    
    // 根据错误级别执行相应操作
    this.handleErrorByLevel(errorInfo)
    
    return errorInfo
  }

  /**
   * 标准化错误对象
   * @param {Error|string} error 错误
   * @param {string} type 错误类型
   * @param {string} level 错误级别
   * @param {Object} context 上下文
   * @returns {Object} 标准化的错误对象
   */
  normalizeError(error, type, level, context) {
    const timestamp = new Date()
    const id = `error_${timestamp.getTime()}_${Math.random().toString(36).substr(2, 9)}`
    
    let message = '未知错误'
    let stack = null
    
    if (error instanceof Error) {
      message = error.message
      stack = error.stack
    } else if (typeof error === 'string') {
      message = error
    } else if (error && error.message) {
      message = error.message
    }
    
    return {
      id,
      message,
      stack,
      type,
      level,
      timestamp,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  /**
   * 记录错误日志
   * @param {Object} errorInfo 错误信息
   */
  logError(errorInfo) {
    // 添加到内存日志
    this.errorLog.unshift(errorInfo)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }
    
    // 控制台输出
    console.error(`[${errorInfo.level.toUpperCase()}] ${errorInfo.type}: ${errorInfo.message}`, errorInfo)
    
    // 发送到服务器（在实际应用中）
    this.sendErrorToServer(errorInfo)
  }

  /**
   * 发送错误到服务器
   * @param {Object} errorInfo 错误信息
   */
  sendErrorToServer(errorInfo) {
    // 在实际应用中，这里会发送错误到服务器
    // 这里只是模拟
    if (errorInfo.level === ErrorLevels.CRITICAL) {
      console.warn('Critical error would be sent to server:', errorInfo)
    }
  }

  /**
   * 显示用户友好的错误提示
   * @param {Object} errorInfo 错误信息
   */
  showUserError(errorInfo) {
    const userMessage = this.getUserFriendlyMessage(errorInfo)
    
    if (errorInfo.level === ErrorLevels.CRITICAL || errorInfo.level === ErrorLevels.HIGH) {
      showError(userMessage, '错误', { autoClose: false })
    } else if (errorInfo.level === ErrorLevels.MEDIUM) {
      showWarning(userMessage, '警告')
    }
    // LOW级别的错误不显示给用户
  }

  /**
   * 获取用户友好的错误消息
   * @param {Object} errorInfo 错误信息
   * @returns {string} 用户友好的消息
   */
  getUserFriendlyMessage(errorInfo) {
    const messageMap = {
      [ErrorTypes.NETWORK]: '网络连接异常，请检查网络设置',
      [ErrorTypes.VALIDATION]: '输入数据不符合要求，请检查后重试',
      [ErrorTypes.AUTHENTICATION]: '身份验证失败，请重新登录',
      [ErrorTypes.AUTHORIZATION]: '权限不足，无法执行此操作',
      [ErrorTypes.SERVER]: '服务器暂时无法响应，请稍后重试',
      [ErrorTypes.CLIENT]: '客户端错误，请刷新页面重试'
    }
    
    return messageMap[errorInfo.type] || '操作失败，请稍后重试'
  }

  /**
   * 根据错误级别处理
   * @param {Object} errorInfo 错误信息
   */
  handleErrorByLevel(errorInfo) {
    switch (errorInfo.level) {
      case ErrorLevels.CRITICAL:
        // 关键错误：可能需要重新加载页面或跳转到错误页面
        console.error('Critical error detected:', errorInfo)
        break
      case ErrorLevels.HIGH:
        // 高级错误：记录详细信息，可能影响用户体验
        console.warn('High level error:', errorInfo)
        break
      case ErrorLevels.MEDIUM:
        // 中级错误：记录信息，用户可以继续使用
        console.info('Medium level error:', errorInfo)
        break
      case ErrorLevels.LOW:
        // 低级错误：仅记录，不影响用户体验
        console.debug('Low level error:', errorInfo)
        break
    }
  }

  /**
   * 获取错误日志
   * @param {number} limit 限制数量
   * @returns {Array} 错误日志列表
   */
  getErrorLog(limit = 50) {
    return this.errorLog.slice(0, limit)
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStats() {
    const stats = {
      total: this.errorLog.length,
      byType: {},
      byLevel: {},
      recent: 0 // 最近1小时的错误数
    }
    
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    
    this.errorLog.forEach(error => {
      // 按类型统计
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      
      // 按级别统计
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1
      
      // 最近错误统计
      if (error.timestamp > oneHourAgo) {
        stats.recent++
      }
    })
    
    return stats
  }
}

// 创建全局错误处理器实例
export const globalErrorHandler = new ErrorHandler()

/**
 * 便捷的错误处理函数
 */
export function handleError(error, type, level, context) {
  return globalErrorHandler.handleError(error, type, level, context)
}

/**
 * 网络错误处理
 */
export function handleNetworkError(error, context = {}) {
  return handleError(error, ErrorTypes.NETWORK, ErrorLevels.HIGH, context)
}

/**
 * 验证错误处理
 */
export function handleValidationError(error, context = {}) {
  return handleError(error, ErrorTypes.VALIDATION, ErrorLevels.MEDIUM, context)
}

/**
 * 服务器错误处理
 */
export function handleServerError(error, context = {}) {
  return handleError(error, ErrorTypes.SERVER, ErrorLevels.HIGH, context)
}

/**
 * 异步操作错误处理装饰器
 */
export function withErrorHandling(asyncFn, errorType = ErrorTypes.UNKNOWN, errorLevel = ErrorLevels.MEDIUM) {
  return async function(...args) {
    try {
      return await asyncFn.apply(this, args)
    } catch (error) {
      handleError(error, errorType, errorLevel, { 
        function: asyncFn.name,
        arguments: args 
      })
      throw error
    }
  }
}

/**
 * Promise错误处理
 */
export function handlePromise(promise, errorType = ErrorTypes.UNKNOWN, errorLevel = ErrorLevels.MEDIUM) {
  return promise.catch(error => {
    handleError(error, errorType, errorLevel)
    throw error
  })
}

export default globalErrorHandler
