{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, vModelCheckbox as _vModelCheckbox, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"password-generator\"\n};\nconst _hoisted_2 = {\n  class: \"mb-6\"\n};\nconst _hoisted_3 = {\n  class: \"relative\"\n};\nconst _hoisted_4 = [\"type\", \"value\"];\nconst _hoisted_5 = {\n  class: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1\"\n};\nconst _hoisted_6 = {\n  class: \"mt-3\"\n};\nconst _hoisted_7 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_8 = {\n  class: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n};\nconst _hoisted_9 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_10 = {\n  class: \"flex items-center space-x-3 cursor-pointer\"\n};\nconst _hoisted_11 = {\n  class: \"flex items-center space-x-3 cursor-pointer\"\n};\nconst _hoisted_12 = {\n  class: \"flex items-center space-x-3 cursor-pointer\"\n};\nconst _hoisted_13 = {\n  class: \"flex items-center space-x-3 cursor-pointer\"\n};\nconst _hoisted_14 = {\n  class: \"border-t border-gray-200 dark:border-gray-700 pt-4\"\n};\nconst _hoisted_15 = {\n  class: \"space-y-3\"\n};\nconst _hoisted_16 = {\n  class: \"flex items-center space-x-3 cursor-pointer\"\n};\nconst _hoisted_17 = {\n  class: \"flex items-center space-x-3 cursor-pointer\"\n};\nconst _hoisted_18 = {\n  class: \"flex items-center space-x-3\"\n};\nconst _hoisted_19 = {\n  class: \"border-t border-gray-200 dark:border-gray-700 pt-4\"\n};\nconst _hoisted_20 = {\n  class: \"grid grid-cols-2 gap-2\"\n};\nconst _hoisted_21 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 生成的密码显示 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[19] || (_cache[19] = _createElementVNode(\"label\", {\n    class: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n  }, \" 生成的密码 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"input\", {\n    type: $data.showPassword ? 'text' : 'password',\n    value: $data.generatedPassword,\n    readonly: \"\",\n    class: _normalizeClass([\"w-full px-4 py-3 pr-20 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white\", $options.passwordStrengthClass])\n  }, null, 10 /* CLASS, PROPS */, _hoisted_4), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args)),\n    class: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\",\n    type: \"button\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', $data.showPassword ? 'eye-slash' : 'eye']\n  }, null, 8 /* PROPS */, [\"icon\"])]), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.copyPassword && $options.copyPassword(...args)),\n    class: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\",\n    type: \"button\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', $data.copied ? 'check' : 'copy'],\n    class: _normalizeClass({\n      'text-green-500': $data.copied\n    })\n  }, null, 8 /* PROPS */, [\"icon\", \"class\"])]), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"p-2 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 transition-colors\",\n    type: \"button\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'sync-alt'],\n    class: _normalizeClass({\n      'animate-spin': $data.generating\n    })\n  }, null, 8 /* PROPS */, [\"class\"])])])]), _createCommentVNode(\" 密码强度指示器 \"), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_PasswordStrengthMeter, {\n    password: $data.generatedPassword,\n    \"show-details\": true,\n    \"show-suggestions\": true\n  }, null, 8 /* PROPS */, [\"password\"])])]), _createCommentVNode(\" 密码选项 \"), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 密码长度 \"), _createElementVNode(\"div\", null, [_createElementVNode(\"label\", _hoisted_8, \" 密码长度: \" + _toDisplayString($data.options.length), 1 /* TEXT */), _withDirectives(_createElementVNode(\"input\", {\n    type: \"range\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.options.length = $event),\n    min: \"8\",\n    max: \"64\",\n    class: \"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\",\n    onInput: _cache[4] || (_cache[4] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.options.length, void 0, {\n    number: true\n  }]]), _cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n    class: \"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\"\n  }, [_createElementVNode(\"span\", null, \"8\"), _createElementVNode(\"span\", null, \"64\")], -1 /* HOISTED */))]), _createCommentVNode(\" 字符类型选项 \"), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"label\", _hoisted_10, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.options.includeUppercase = $event),\n    onChange: _cache[6] || (_cache[6] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $data.options.includeUppercase]]), _cache[21] || (_cache[21] = _createElementVNode(\"span\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"大写字母 (A-Z)\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_11, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.options.includeLowercase = $event),\n    onChange: _cache[8] || (_cache[8] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $data.options.includeLowercase]]), _cache[22] || (_cache[22] = _createElementVNode(\"span\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"小写字母 (a-z)\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_12, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.options.includeNumbers = $event),\n    onChange: _cache[10] || (_cache[10] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $data.options.includeNumbers]]), _cache[23] || (_cache[23] = _createElementVNode(\"span\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"数字 (0-9)\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_13, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.options.includeSymbols = $event),\n    onChange: _cache[12] || (_cache[12] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $data.options.includeSymbols]]), _cache[24] || (_cache[24] = _createElementVNode(\"span\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"特殊字符 (!@#$)\", -1 /* HOISTED */))])]), _createCommentVNode(\" 高级选项 \"), _createElementVNode(\"div\", _hoisted_14, [_cache[28] || (_cache[28] = _createElementVNode(\"h4\", {\n    class: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\"\n  }, \"高级选项\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"label\", _hoisted_16, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.options.excludeSimilar = $event),\n    onChange: _cache[14] || (_cache[14] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $data.options.excludeSimilar]]), _cache[25] || (_cache[25] = _createElementVNode(\"span\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"排除相似字符 (0, O, l, 1, I)\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_17, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.options.excludeAmbiguous = $event),\n    onChange: _cache[16] || (_cache[16] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    class: \"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $data.options.excludeAmbiguous]]), _cache[26] || (_cache[26] = _createElementVNode(\"span\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"排除歧义字符 (\" + _toDisplayString('{') + \" \" + _toDisplayString('}') + \" [ ] ( ) / \\\\ ' \\\" ~ , ; . < >)\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_18, [_cache[27] || (_cache[27] = _createElementVNode(\"label\", {\n    class: \"text-sm text-gray-700 dark:text-gray-300\"\n  }, \"自定义字符:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.options.customChars = $event),\n    onInput: _cache[18] || (_cache[18] = (...args) => $options.generatePassword && $options.generatePassword(...args)),\n    placeholder: \"添加自定义字符\",\n    class: \"flex-1 px-3 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:text-white\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.options.customChars]])])])]), _createCommentVNode(\" 预设模板 \"), _createElementVNode(\"div\", _hoisted_19, [_cache[29] || (_cache[29] = _createElementVNode(\"h4\", {\n    class: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\"\n  }, \"快速模板\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.templates, template => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: template.name,\n      onClick: $event => $options.applyTemplate(template),\n      class: \"px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors\"\n    }, _toDisplayString(template.name), 9 /* TEXT, PROPS */, _hoisted_21);\n  }), 128 /* KEYED_FRAGMENT */))])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "type", "$data", "showPassword", "value", "generatedPassword", "readonly", "_normalizeClass", "$options", "passwordStrengthClass", "_hoisted_4", "_hoisted_5", "onClick", "_cache", "args", "togglePasswordVisibility", "_createVNode", "_component_font_awesome_icon", "icon", "copyPassword", "copied", "generatePassword", "generating", "_hoisted_6", "_component_PasswordStrengthMeter", "password", "_hoisted_7", "_hoisted_8", "_toDisplayString", "options", "length", "$event", "min", "max", "onInput", "number", "_hoisted_9", "_hoisted_10", "includeUppercase", "onChange", "_hoisted_11", "includeLowercase", "_hoisted_12", "includeNumbers", "_hoisted_13", "includeSymbols", "_hoisted_14", "_hoisted_15", "_hoisted_16", "excludeS<PERSON><PERSON>r", "_hoisted_17", "excludeAmbiguous", "_hoisted_18", "customChars", "placeholder", "_hoisted_19", "_hoisted_20", "_Fragment", "_renderList", "templates", "template", "key", "name", "applyTemplate", "_hoisted_21"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\AdvancedPasswordGenerator.vue"], "sourcesContent": ["<template>\n  <div class=\"password-generator\">\n    <!-- 生成的密码显示 -->\n    <div class=\"mb-6\">\n      <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n        生成的密码\n      </label>\n      <div class=\"relative\">\n        <input\n          :type=\"showPassword ? 'text' : 'password'\"\n          :value=\"generatedPassword\"\n          readonly\n          class=\"w-full px-4 py-3 pr-20 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white\"\n          :class=\"passwordStrengthClass\"\n        />\n        <div class=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1\">\n          <button\n            @click=\"togglePasswordVisibility\"\n            class=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n            type=\"button\"\n          >\n            <font-awesome-icon :icon=\"['fas', showPassword ? 'eye-slash' : 'eye']\" />\n          </button>\n          <button\n            @click=\"copyPassword\"\n            class=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n            type=\"button\"\n          >\n            <font-awesome-icon :icon=\"['fas', copied ? 'check' : 'copy']\" :class=\"{ 'text-green-500': copied }\" />\n          </button>\n          <button\n            @click=\"generatePassword\"\n            class=\"p-2 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 transition-colors\"\n            type=\"button\"\n          >\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" :class=\"{ 'animate-spin': generating }\" />\n          </button>\n        </div>\n      </div>\n      \n      <!-- 密码强度指示器 -->\n      <div class=\"mt-3\">\n        <PasswordStrengthMeter\n          :password=\"generatedPassword\"\n          :show-details=\"true\"\n          :show-suggestions=\"true\"\n        />\n      </div>\n    </div>\n\n    <!-- 密码选项 -->\n    <div class=\"space-y-4\">\n      <!-- 密码长度 -->\n      <div>\n        <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          密码长度: {{ options.length }}\n        </label>\n        <input\n          type=\"range\"\n          v-model.number=\"options.length\"\n          min=\"8\"\n          max=\"64\"\n          class=\"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n          @input=\"generatePassword\"\n        />\n        <div class=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          <span>8</span>\n          <span>64</span>\n        </div>\n      </div>\n\n      <!-- 字符类型选项 -->\n      <div class=\"grid grid-cols-2 gap-4\">\n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeUppercase\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">大写字母 (A-Z)</span>\n        </label>\n        \n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeLowercase\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">小写字母 (a-z)</span>\n        </label>\n        \n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeNumbers\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">数字 (0-9)</span>\n        </label>\n        \n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeSymbols\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">特殊字符 (!@#$)</span>\n        </label>\n      </div>\n\n      <!-- 高级选项 -->\n      <div class=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\n        <h4 class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">高级选项</h4>\n        \n        <div class=\"space-y-3\">\n          <label class=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              v-model=\"options.excludeSimilar\"\n              @change=\"generatePassword\"\n              class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n            />\n            <span class=\"text-sm text-gray-700 dark:text-gray-300\">排除相似字符 (0, O, l, 1, I)</span>\n          </label>\n          \n          <label class=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              v-model=\"options.excludeAmbiguous\"\n              @change=\"generatePassword\"\n              class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n            />\n            <span class=\"text-sm text-gray-700 dark:text-gray-300\">排除歧义字符 ({{ '{' }} {{ '}' }} [ ] ( ) / \\ ' \" ~ , ; . &lt; &gt;)</span>\n          </label>\n          \n          <div class=\"flex items-center space-x-3\">\n            <label class=\"text-sm text-gray-700 dark:text-gray-300\">自定义字符:</label>\n            <input\n              type=\"text\"\n              v-model=\"options.customChars\"\n              @input=\"generatePassword\"\n              placeholder=\"添加自定义字符\"\n              class=\"flex-1 px-3 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:text-white\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- 预设模板 -->\n      <div class=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\n        <h4 class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">快速模板</h4>\n        <div class=\"grid grid-cols-2 gap-2\">\n          <button\n            v-for=\"template in templates\"\n            :key=\"template.name\"\n            @click=\"applyTemplate(template)\"\n            class=\"px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors\"\n          >\n            {{ template.name }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport PasswordStrengthMeter from './PasswordStrengthMeter.vue'\nimport { generateSecurePassword, checkPasswordStrength } from '@/utils/passwordUtils'\nimport { handleError, ErrorTypes, ErrorLevels } from '@/utils/errorHandler'\n\nexport default {\n  name: 'AdvancedPasswordGenerator',\n  components: {\n    PasswordStrengthMeter\n  },\n  emits: ['password-generated'],\n  props: {\n    initialOptions: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      generatedPassword: '',\n      showPassword: false,\n      copied: false,\n      generating: false,\n      options: {\n        length: 16,\n        includeUppercase: true,\n        includeLowercase: true,\n        includeNumbers: true,\n        includeSymbols: true,\n        excludeSimilar: false,\n        excludeAmbiguous: false,\n        customChars: ''\n      },\n      templates: [\n        {\n          name: '高强度',\n          options: { length: 20, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: true }\n        },\n        {\n          name: '标准',\n          options: { length: 12, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: false }\n        },\n        {\n          name: '简单',\n          options: { length: 8, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: false }\n        },\n        {\n          name: '数字密码',\n          options: { length: 6, includeUppercase: false, includeLowercase: false, includeNumbers: true, includeSymbols: false }\n        }\n      ]\n    }\n  },\n  computed: {\n    passwordStrength() {\n      return checkPasswordStrength(this.generatedPassword)\n    },\n    passwordStrengthClass() {\n      const strength = this.passwordStrength.strength\n      if (strength >= 4) return 'border-green-500 dark:border-green-400'\n      if (strength >= 3) return 'border-yellow-500 dark:border-yellow-400'\n      if (strength >= 2) return 'border-orange-500 dark:border-orange-400'\n      return 'border-red-500 dark:border-red-400'\n    }\n  },\n  mounted() {\n    // 应用初始选项\n    Object.assign(this.options, this.initialOptions)\n    this.generatePassword()\n  },\n  methods: {\n    generatePassword() {\n      this.generating = true\n      \n      setTimeout(() => {\n        let charset = ''\n        \n        if (this.options.includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n        if (this.options.includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz'\n        if (this.options.includeNumbers) charset += '0123456789'\n        if (this.options.includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'\n        \n        if (this.options.customChars) {\n          charset += this.options.customChars\n        }\n        \n        if (this.options.excludeSimilar) {\n          charset = charset.replace(/[0Ol1I]/g, '')\n        }\n        \n        if (this.options.excludeAmbiguous) {\n          charset = charset.replace(/[{}[\\]()/\\\\'\"~,;.<>]/g, '')\n        }\n        \n        if (!charset) {\n          this.generatedPassword = ''\n          this.generating = false\n          return\n        }\n        \n        let password = ''\n        for (let i = 0; i < this.options.length; i++) {\n          password += charset.charAt(Math.floor(Math.random() * charset.length))\n        }\n        \n        this.generatedPassword = password\n        this.generating = false\n        \n        // 发出事件\n        this.$emit('password-generated', password)\n      }, 100)\n    },\n    \n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword\n    },\n    \n    async copyPassword() {\n      try {\n        await navigator.clipboard.writeText(this.generatedPassword)\n        this.copied = true\n        setTimeout(() => {\n          this.copied = false\n        }, 2000)\n      } catch (err) {\n        console.error('复制失败:', err)\n      }\n    },\n    \n    applyTemplate(template) {\n      Object.assign(this.options, template.options)\n      this.generatePassword()\n    },\n    \n    calculatePasswordStrength(password) {\n      let score = 0\n      let feedback = []\n      \n      if (!password) return { score: 0, feedback: ['请生成密码'] }\n      \n      // 长度检查\n      if (password.length >= 12) score += 1\n      else if (password.length >= 8) score += 0.5\n      else feedback.push('密码长度至少8位')\n      \n      // 字符类型检查\n      if (/[a-z]/.test(password)) score += 0.5\n      if (/[A-Z]/.test(password)) score += 0.5\n      if (/[0-9]/.test(password)) score += 0.5\n      if (/[^a-zA-Z0-9]/.test(password)) score += 1\n      \n      // 复杂度检查\n      const uniqueChars = new Set(password).size\n      if (uniqueChars >= password.length * 0.7) score += 0.5\n      \n      return {\n        score: Math.min(4, Math.round(score)),\n        feedback\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.slider::-webkit-slider-thumb {\n  appearance: none;\n  height: 20px;\n  width: 20px;\n  border-radius: 50%;\n  background: #3b82f6;\n  cursor: pointer;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.slider::-moz-range-thumb {\n  height: 20px;\n  width: 20px;\n  border-radius: 50%;\n  background: #3b82f6;\n  cursor: pointer;\n  border: none;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAM;;EAIVA,KAAK,EAAC;AAAU;mBAP3B;;EAeaA,KAAK,EAAC;AAAoE;;EA0B5EA,KAAK,EAAC;AAAM;;EAUdA,KAAK,EAAC;AAAW;;EAGXA,KAAK,EAAC;AAAiE;;EAkB3EA,KAAK,EAAC;AAAwB;;EAC1BA,KAAK,EAAC;AAA4C;;EAUlDA,KAAK,EAAC;AAA4C;;EAUlDA,KAAK,EAAC;AAA4C;;EAUlDA,KAAK,EAAC;AAA4C;;EAYtDA,KAAK,EAAC;AAAoD;;EAGxDA,KAAK,EAAC;AAAW;;EACbA,KAAK,EAAC;AAA4C;;EAUlDA,KAAK,EAAC;AAA4C;;EAUpDA,KAAK,EAAC;AAA6B;;EAcvCA,KAAK,EAAC;AAAoD;;EAExDA,KAAK,EAAC;AAAwB;oBA3J3C;;;;uBACEC,mBAAA,CAsKM,OAtKNC,UAsKM,GArKJC,mBAAA,aAAgB,EAChBC,mBAAA,CA6CM,OA7CNC,UA6CM,G,4BA5CJD,mBAAA,CAEQ;IAFDJ,KAAK,EAAC;EAAiE,GAAC,SAE/E,sBACAI,mBAAA,CA+BM,OA/BNE,UA+BM,GA9BJF,mBAAA,CAME;IALCG,IAAI,EAAEC,KAAA,CAAAC,YAAY;IAClBC,KAAK,EAAEF,KAAA,CAAAG,iBAAiB;IACzBC,QAAQ,EAAR,EAAQ;IACRZ,KAAK,EAZfa,eAAA,EAYgB,iMAAiM,EAC/LC,QAAA,CAAAC,qBAAqB;kCAbvCC,UAAA,GAeQZ,mBAAA,CAsBM,OAtBNa,UAsBM,GArBJb,mBAAA,CAMS;IALNc,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,QAAA,CAAAO,wBAAA,IAAAP,QAAA,CAAAO,wBAAA,IAAAD,IAAA,CAAwB;IAChCpB,KAAK,EAAC,qGAAqG;IAC3GO,IAAI,EAAC;MAELe,YAAA,CAAyEC,4BAAA;IAArDC,IAAI,UAAUhB,KAAA,CAAAC,YAAY;uCAEhDL,mBAAA,CAMS;IALNc,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,QAAA,CAAAW,YAAA,IAAAX,QAAA,CAAAW,YAAA,IAAAL,IAAA,CAAY;IACpBpB,KAAK,EAAC,qGAAqG;IAC3GO,IAAI,EAAC;MAELe,YAAA,CAAsGC,4BAAA;IAAlFC,IAAI,UAAUhB,KAAA,CAAAkB,MAAM;IAAuB1B,KAAK,EA5BhFa,eAAA;MAAA,kBA4BsGL,KAAA,CAAAkB;IAAM;gDAElGtB,mBAAA,CAMS;IALNc,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACxBpB,KAAK,EAAC,qGAAqG;IAC3GO,IAAI,EAAC;MAELe,YAAA,CAAyFC,4BAAA;IAArEC,IAAI,EAAE,mBAAmB;IAAGxB,KAAK,EAnCjEa,eAAA;MAAA,gBAmCqFL,KAAA,CAAAoB;IAAU;4CAKzFzB,mBAAA,aAAgB,EAChBC,mBAAA,CAMM,OANNyB,UAMM,GALJP,YAAA,CAIEQ,gCAAA;IAHCC,QAAQ,EAAEvB,KAAA,CAAAG,iBAAiB;IAC3B,cAAY,EAAE,IAAI;IAClB,kBAAgB,EAAE;6CAKzBR,mBAAA,UAAa,EACbC,mBAAA,CAmHM,OAnHN4B,UAmHM,GAlHJ7B,mBAAA,UAAa,EACbC,mBAAA,CAgBM,cAfJA,mBAAA,CAEQ,SAFR6B,UAEQ,EAFuE,SACvE,GAAAC,gBAAA,CAAG1B,KAAA,CAAA2B,OAAO,CAACC,MAAM,kB,gBAEzBhC,mBAAA,CAOE;IANAG,IAAI,EAAC,OAAO;IA1DtB,uBAAAY,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IA2D0B7B,KAAA,CAAA2B,OAAO,CAACC,MAAM,GAAAC,MAAA;IAC9BC,GAAG,EAAC,GAAG;IACPC,GAAG,EAAC,IAAI;IACRvC,KAAK,EAAC,0FAA0F;IAC/FwC,OAAK,EAAArB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;iEAJRZ,KAAA,CAAA2B,OAAO,CAACC,MAAM,E;IAAtBK,MAAM,EAAd;EAA+B,E,gCAMjCrC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAoE,IAC7EI,mBAAA,CAAc,cAAR,GAAC,GACPA,mBAAA,CAAe,cAAT,IAAE,E,wBAIZD,mBAAA,YAAe,EACfC,mBAAA,CAwCM,OAxCNsC,UAwCM,GAvCJtC,mBAAA,CAQQ,SARRuC,WAQQ,G,gBAPNvC,mBAAA,CAKE;IAJAG,IAAI,EAAC,UAAU;IA3E3B,uBAAAY,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IA4EqB7B,KAAA,CAAA2B,OAAO,CAACS,gBAAgB,GAAAP,MAAA;IAChCQ,QAAM,EAAA1B,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACzBpB,KAAK,EAAC;qEAFGQ,KAAA,CAAA2B,OAAO,CAACS,gBAAgB,E,+BAInCxC,mBAAA,CAAwE;IAAlEJ,KAAK,EAAC;EAA0C,GAAC,YAAU,qB,GAGnEI,mBAAA,CAQQ,SARR0C,WAQQ,G,gBAPN1C,mBAAA,CAKE;IAJAG,IAAI,EAAC,UAAU;IArF3B,uBAAAY,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAsFqB7B,KAAA,CAAA2B,OAAO,CAACY,gBAAgB,GAAAV,MAAA;IAChCQ,QAAM,EAAA1B,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACzBpB,KAAK,EAAC;qEAFGQ,KAAA,CAAA2B,OAAO,CAACY,gBAAgB,E,+BAInC3C,mBAAA,CAAwE;IAAlEJ,KAAK,EAAC;EAA0C,GAAC,YAAU,qB,GAGnEI,mBAAA,CAQQ,SARR4C,WAQQ,G,gBAPN5C,mBAAA,CAKE;IAJAG,IAAI,EAAC,UAAU;IA/F3B,uBAAAY,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAgGqB7B,KAAA,CAAA2B,OAAO,CAACc,cAAc,GAAAZ,MAAA;IAC9BQ,QAAM,EAAA1B,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACzBpB,KAAK,EAAC;qEAFGQ,KAAA,CAAA2B,OAAO,CAACc,cAAc,E,+BAIjC7C,mBAAA,CAAsE;IAAhEJ,KAAK,EAAC;EAA0C,GAAC,UAAQ,qB,GAGjEI,mBAAA,CAQQ,SARR8C,WAQQ,G,gBAPN9C,mBAAA,CAKE;IAJAG,IAAI,EAAC,UAAU;IAzG3B,uBAAAY,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IA0GqB7B,KAAA,CAAA2B,OAAO,CAACgB,cAAc,GAAAd,MAAA;IAC9BQ,QAAM,EAAA1B,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACzBpB,KAAK,EAAC;qEAFGQ,KAAA,CAAA2B,OAAO,CAACgB,cAAc,E,+BAIjC/C,mBAAA,CAAyE;IAAnEJ,KAAK,EAAC;EAA0C,GAAC,aAAW,qB,KAItEG,mBAAA,UAAa,EACbC,mBAAA,CAmCM,OAnCNgD,WAmCM,G,4BAlCJhD,mBAAA,CAA+E;IAA3EJ,KAAK,EAAC;EAA2D,GAAC,MAAI,sBAE1EI,mBAAA,CA+BM,OA/BNiD,WA+BM,GA9BJjD,mBAAA,CAQQ,SARRkD,WAQQ,G,gBAPNlD,mBAAA,CAKE;IAJAG,IAAI,EAAC,UAAU;IAzH7B,uBAAAY,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IA0HuB7B,KAAA,CAAA2B,OAAO,CAACoB,cAAc,GAAAlB,MAAA;IAC9BQ,QAAM,EAAA1B,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACzBpB,KAAK,EAAC;qEAFGQ,KAAA,CAAA2B,OAAO,CAACoB,cAAc,E,+BAIjCnD,mBAAA,CAAoF;IAA9EJ,KAAK,EAAC;EAA0C,GAAC,wBAAsB,qB,GAG/EI,mBAAA,CAQQ,SARRoD,WAQQ,G,gBAPNpD,mBAAA,CAKE;IAJAG,IAAI,EAAC,UAAU;IAnI7B,uBAAAY,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAoIuB7B,KAAA,CAAA2B,OAAO,CAACsB,gBAAgB,GAAApB,MAAA;IAChCQ,QAAM,EAAA1B,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACzBpB,KAAK,EAAC;qEAFGQ,KAAA,CAAA2B,OAAO,CAACsB,gBAAgB,E,+BAInCrD,mBAAA,CAA4H;IAAtHJ,KAAK,EAAC;EAA0C,GAAC,UAAQ,GAAAkC,gBAAA,CAAG,GAAG,IAAG,GAAC,GAAAA,gBAAA,CAAG,GAAG,IAAG,iCAAmC,qB,GAGvH9B,mBAAA,CASM,OATNsD,WASM,G,4BARJtD,mBAAA,CAAsE;IAA/DJ,KAAK,EAAC;EAA0C,GAAC,QAAM,sB,gBAC9DI,mBAAA,CAME;IALAG,IAAI,EAAC,MAAM;IA9IzB,uBAAAY,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IA+IuB7B,KAAA,CAAA2B,OAAO,CAACwB,WAAW,GAAAtB,MAAA;IAC3BG,OAAK,EAAArB,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEN,QAAA,CAAAa,gBAAA,IAAAb,QAAA,CAAAa,gBAAA,IAAAP,IAAA,CAAgB;IACxBwC,WAAW,EAAC,SAAS;IACrB5D,KAAK,EAAC;iEAHGQ,KAAA,CAAA2B,OAAO,CAACwB,WAAW,E,SASpCxD,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZNyD,WAYM,G,4BAXJzD,mBAAA,CAA+E;IAA3EJ,KAAK,EAAC;EAA2D,GAAC,MAAI,sBAC1EI,mBAAA,CASM,OATN0D,WASM,I,kBARJ7D,mBAAA,CAOS8D,SAAA,QAnKnBC,WAAA,CA6J+BxD,KAAA,CAAAyD,SAAS,EAArBC,QAAQ;yBADjBjE,mBAAA,CAOS;MALNkE,GAAG,EAAED,QAAQ,CAACE,IAAI;MAClBlD,OAAK,EAAAmB,MAAA,IAAEvB,QAAA,CAAAuD,aAAa,CAACH,QAAQ;MAC9BlE,KAAK,EAAC;wBAEHkE,QAAQ,CAACE,IAAI,wBAlK5BE,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}