/**
 * 性能监控工具类
 * 提供页面性能监控、组件性能分析等功能
 */

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.timers = new Map()
    this.observers = new Map()
    this.setupPerformanceObserver()
  }

  /**
   * 设置性能观察器
   */
  setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      // 监控导航性能
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordNavigationMetrics(entry)
        }
      })
      navObserver.observe({ entryTypes: ['navigation'] })
      this.observers.set('navigation', navObserver)

      // 监控资源加载性能
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordResourceMetrics(entry)
        }
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.set('resource', resourceObserver)

      // 监控长任务
      if ('PerformanceObserver' in window && 'PerformanceLongTaskTiming' in window) {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordLongTask(entry)
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.set('longtask', longTaskObserver)
      }
    }
  }

  /**
   * 记录导航性能指标
   */
  recordNavigationMetrics(entry) {
    const metrics = {
      // 页面加载时间
      loadTime: entry.loadEventEnd - entry.loadEventStart,
      // DOM内容加载时间
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      // 首次内容绘制
      firstContentfulPaint: this.getFirstContentfulPaint(),
      // 最大内容绘制
      largestContentfulPaint: this.getLargestContentfulPaint(),
      // 累积布局偏移
      cumulativeLayoutShift: this.getCumulativeLayoutShift(),
      // 首次输入延迟
      firstInputDelay: this.getFirstInputDelay(),
      // DNS查询时间
      dnsLookup: entry.domainLookupEnd - entry.domainLookupStart,
      // TCP连接时间
      tcpConnect: entry.connectEnd - entry.connectStart,
      // 请求响应时间
      requestResponse: entry.responseEnd - entry.requestStart,
      // DOM解析时间
      domParsing: entry.domInteractive - entry.responseEnd,
      // 资源加载时间
      resourceLoad: entry.loadEventStart - entry.domContentLoadedEventEnd
    }

    this.metrics.set('navigation', {
      ...metrics,
      timestamp: Date.now(),
      url: window.location.href
    })

    console.log('Navigation Performance:', metrics)
  }

  /**
   * 记录资源加载性能
   */
  recordResourceMetrics(entry) {
    const resourceMetrics = this.metrics.get('resources') || []
    
    resourceMetrics.push({
      name: entry.name,
      type: entry.initiatorType,
      size: entry.transferSize,
      duration: entry.duration,
      startTime: entry.startTime,
      timestamp: Date.now()
    })

    // 只保留最近的100个资源记录
    if (resourceMetrics.length > 100) {
      resourceMetrics.splice(0, resourceMetrics.length - 100)
    }

    this.metrics.set('resources', resourceMetrics)
  }

  /**
   * 记录长任务
   */
  recordLongTask(entry) {
    const longTasks = this.metrics.get('longTasks') || []
    
    longTasks.push({
      duration: entry.duration,
      startTime: entry.startTime,
      timestamp: Date.now()
    })

    // 只保留最近的50个长任务记录
    if (longTasks.length > 50) {
      longTasks.splice(0, longTasks.length - 50)
    }

    this.metrics.set('longTasks', longTasks)
    
    // 长任务警告
    if (entry.duration > 100) {
      console.warn(`Long task detected: ${entry.duration}ms`)
    }
  }

  /**
   * 开始计时
   */
  startTimer(name) {
    this.timers.set(name, {
      startTime: performance.now(),
      startTimestamp: Date.now()
    })
  }

  /**
   * 结束计时
   */
  endTimer(name) {
    const timer = this.timers.get(name)
    if (!timer) {
      console.warn(`Timer "${name}" not found`)
      return null
    }

    const duration = performance.now() - timer.startTime
    this.timers.delete(name)

    // 记录自定义性能指标
    const customMetrics = this.metrics.get('custom') || []
    customMetrics.push({
      name,
      duration,
      timestamp: timer.startTimestamp
    })

    this.metrics.set('custom', customMetrics)

    return duration
  }

  /**
   * 测量函数执行时间
   */
  measureFunction(fn, name) {
    return (...args) => {
      this.startTimer(name)
      const result = fn.apply(this, args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          this.endTimer(name)
        })
      } else {
        this.endTimer(name)
        return result
      }
    }
  }

  /**
   * 获取首次内容绘制时间
   */
  getFirstContentfulPaint() {
    const entries = performance.getEntriesByType('paint')
    const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : null
  }

  /**
   * 获取最大内容绘制时间
   */
  getLargestContentfulPaint() {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          resolve(lastEntry ? lastEntry.startTime : null)
        })
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
        
        // 超时处理
        setTimeout(() => {
          observer.disconnect()
          resolve(null)
        }, 10000)
      } else {
        resolve(null)
      }
    })
  }

  /**
   * 获取累积布局偏移
   */
  getCumulativeLayoutShift() {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        let clsValue = 0
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          }
        })
        observer.observe({ entryTypes: ['layout-shift'] })
        
        // 5秒后返回结果
        setTimeout(() => {
          observer.disconnect()
          resolve(clsValue)
        }, 5000)
      } else {
        resolve(null)
      }
    })
  }

  /**
   * 获取首次输入延迟
   */
  getFirstInputDelay() {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            resolve(entry.processingStart - entry.startTime)
            observer.disconnect()
            return
          }
        })
        observer.observe({ entryTypes: ['first-input'] })
        
        // 超时处理
        setTimeout(() => {
          observer.disconnect()
          resolve(null)
        }, 10000)
      } else {
        resolve(null)
      }
    })
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      }
    }
    return null
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics() {
    return {
      navigation: this.metrics.get('navigation'),
      resources: this.metrics.get('resources') || [],
      longTasks: this.metrics.get('longTasks') || [],
      custom: this.metrics.get('custom') || [],
      memory: this.getMemoryUsage(),
      timestamp: Date.now()
    }
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const metrics = this.getAllMetrics()
    const report = {
      summary: {
        pageLoadTime: metrics.navigation?.loadTime || 0,
        resourceCount: metrics.resources.length,
        longTaskCount: metrics.longTasks.length,
        memoryUsage: metrics.memory?.usedJSHeapSize || 0
      },
      details: metrics,
      recommendations: this.generateRecommendations(metrics)
    }

    return report
  }

  /**
   * 生成性能优化建议
   */
  generateRecommendations(metrics) {
    const recommendations = []

    // 页面加载时间建议
    if (metrics.navigation?.loadTime > 3000) {
      recommendations.push('页面加载时间过长，建议优化资源加载')
    }

    // 长任务建议
    if (metrics.longTasks.length > 5) {
      recommendations.push('检测到多个长任务，建议优化JavaScript执行')
    }

    // 内存使用建议
    if (metrics.memory?.usedJSHeapSize > 50 * 1024 * 1024) {
      recommendations.push('内存使用较高，建议检查内存泄漏')
    }

    // 资源加载建议
    const largeResources = metrics.resources.filter(r => r.size > 1024 * 1024)
    if (largeResources.length > 0) {
      recommendations.push('检测到大型资源文件，建议进行压缩或分割')
    }

    return recommendations
  }

  /**
   * 清理监控器
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    this.metrics.clear()
    this.timers.clear()
  }
}

// 创建全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor()

/**
 * 便捷的性能测量函数
 */
export function measurePerformance(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      performanceMonitor.startTimer(`${target.constructor.name}.${propertyKey}`)
      const result = originalMethod.apply(this, args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          performanceMonitor.endTimer(`${target.constructor.name}.${propertyKey}`)
        })
      } else {
        performanceMonitor.endTimer(`${target.constructor.name}.${propertyKey}`)
        return result
      }
    }
    
    return descriptor
  }
}

export default performanceMonitor
