/**
 * 密码管理工具类
 */

/**
 * 生成随机密码
 * @param {number} length 密码长度
 * @param {object} options 密码生成选项
 * @returns {string} 生成的密码
 */
export function generatePassword(length = 12, options = {}) {
    const {
        includeUpper = true,
        includeLower = true,
        includeNumbers = true,
        includeSpecial = true
    } = options

    // 字符集
    const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const lowerChars = 'abcdefghijklmnopqrstuvwxyz'
    const numberChars = '0123456789'
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'

    // 构建字符集
    let chars = ''
    if (includeUpper) chars += upperChars
    if (includeLower) chars += lowerChars
    if (includeNumbers) chars += numberChars
    if (includeSpecial) chars += specialChars

    // 确保至少有一个字符集
    if (chars.length === 0) {
        chars = lowerChars + numberChars
    }

    // 生成密码
    let password = ''
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * chars.length)
        password += chars[randomIndex]
    }

    // 确保密码满足要求
    const hasUpper = includeUpper ? /[A-Z]/.test(password) : true
    const hasLower = includeLower ? /[a-z]/.test(password) : true
    const hasNumbers = includeNumbers ? /\d/.test(password) : true
    const hasSpecial = includeSpecial ? /[^A-Za-z0-9]/.test(password) : true

    // 如果不满足要求，重新生成
    if (!hasUpper || !hasLower || !hasNumbers || !hasSpecial) {
        return generatePassword(length, options)
    }

    return password
}

/**
 * 计算密码强度
 * @param {string} password 密码
 * @returns {number} 强度得分 (0-4)
 */
export function calculatePasswordStrength(password) {
    if (!password) return 0

    let score = 0

    // 长度评分
    if (password.length >= 8) score += 1
    if (password.length >= 12) score += 1

    // 字符多样性评分
    if (/[A-Z]/.test(password)) score += 0.5
    if (/[a-z]/.test(password)) score += 0.5
    if (/\d/.test(password)) score += 0.5
    if (/[^A-Za-z0-9]/.test(password)) score += 0.5

    // 复杂度评分
    const variety = (/[A-Z]/.test(password) ? 1 : 0)
        + (/[a-z]/.test(password) ? 1 : 0)
        + (/\d/.test(password) ? 1 : 0)
        + (/[^A-Za-z0-9]/.test(password) ? 1 : 0)

    if (variety >= 3) score += 1

    // 返回整数得分，最高4分
    return Math.min(4, Math.floor(score))
}

/**
 * 检查密码是否符合策略要求
 * @param {string} password 密码
 * @param {object} policy 密码策略
 * @returns {object} 检查结果 { valid: boolean, errors: string[] }
 */
export function validatePasswordAgainstPolicy(password, policy) {
    const errors = []

    // 检查长度
    if (password.length < policy.minLength) {
        errors.push(`密码长度必须至少为 ${policy.minLength} 位`)
    }

    // 检查大写字母
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
        errors.push('密码必须包含至少一个大写字母')
    }

    // 检查小写字母
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
        errors.push('密码必须包含至少一个小写字母')
    }

    // 检查数字
    if (policy.requireNumbers && !/\d/.test(password)) {
        errors.push('密码必须包含至少一个数字')
    }

    // 检查特殊字符
    if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {
        errors.push('密码必须包含至少一个特殊字符')
    }

    // 检查用户名包含
    if (policy.forbidUsername && policy.username) {
        const username = policy.username.toLowerCase()
        if (password.toLowerCase().includes(username)) {
            errors.push('密码不能包含用户名')
        }
    }

    return {
        valid: errors.length === 0,
        errors
    }
}

/**
 * 获取密码强度描述
 * @param {number} strength 密码强度 (0-4)
 * @returns {object} 描述信息 { text: string, colorClass: string }
 */
export function getPasswordStrengthInfo(strength) {
    switch (strength) {
        case 0:
            return { text: '请输入密码', colorClass: 'text-gray-500' }
        case 1:
            return { text: '弱', colorClass: 'text-red-600' }
        case 2:
            return { text: '中等', colorClass: 'text-yellow-600' }
        case 3:
            return { text: '强', colorClass: 'text-green-600' }
        case 4:
            return { text: '非常强', colorClass: 'text-green-600' }
        default:
            return { text: '', colorClass: '' }
    }
}

/**
 * 计算密码过期状态
 * @param {string} lastChangeDate 上次修改日期
 * @param {number} expiryDays 过期天数
 * @returns {object} 过期状态信息
 */
export function calculatePasswordExpiry(lastChangeDate, expiryDays) {
    if (!lastChangeDate || !expiryDays) {
        return { status: 'unknown', daysRemaining: 0, text: '未知' }
    }

    const lastChange = new Date(lastChangeDate)
    const now = new Date()
    const expiryDate = new Date(lastChange)
    expiryDate.setDate(expiryDate.getDate() + expiryDays)

    const diffTime = expiryDate - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    let status = 'normal'
    let text = ''

    if (diffDays < 0) {
        status = 'expired'
        text = `已过期${Math.abs(diffDays)}天`
    } else if (diffDays === 0) {
        status = 'expiring'
        text = '今天过期'
    } else if (diffDays <= 3) {
        status = 'critical'
        text = `${diffDays}天后过期`
    } else if (diffDays <= 7) {
        status = 'warning'
        text = `${diffDays}天后过期`
    } else if (diffDays <= 30) {
        status = 'notice'
        text = `${diffDays}天后过期`
    } else {
        status = 'normal'
        text = `${diffDays}天后过期`
    }

    return { status, daysRemaining: diffDays, text, expiryDate }
}

/**
 * 生成密码建议
 * @param {string} password 当前密码
 * @param {object} policy 密码策略
 * @returns {array} 建议列表
 */
export function generatePasswordSuggestions(password, policy = {}) {
    const suggestions = []

    if (!password) {
        suggestions.push('请输入密码')
        return suggestions
    }

    // 长度建议
    if (password.length < (policy.minLength || 8)) {
        suggestions.push(`密码长度至少需要${policy.minLength || 8}位`)
    } else if (password.length < 12) {
        suggestions.push('建议使用12位以上的密码以提高安全性')
    }

    // 字符类型建议
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
        suggestions.push('添加大写字母')
    }
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
        suggestions.push('添加小写字母')
    }
    if (policy.requireNumbers && !/\d/.test(password)) {
        suggestions.push('添加数字')
    }
    if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {
        suggestions.push('添加特殊字符')
    }

    // 安全性建议
    if (/(.)\1{2,}/.test(password)) {
        suggestions.push('避免连续重复的字符')
    }
    if (/123|abc|qwe/i.test(password)) {
        suggestions.push('避免使用连续的字符序列')
    }

    // 常见密码检查
    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root']
    if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
        suggestions.push('避免使用常见的密码模式')
    }

    if (suggestions.length === 0) {
        suggestions.push('密码强度良好')
    }

    return suggestions
}

/**
 * 格式化密码显示
 * @param {string} password 密码
 * @param {boolean} masked 是否遮罩显示
 * @param {number} visibleChars 显示的字符数
 * @returns {string} 格式化后的密码
 */
export function formatPasswordDisplay(password, masked = true, visibleChars = 0) {
    if (!password) return ''

    if (!masked) {
        return password
    }

    if (visibleChars > 0 && visibleChars < password.length) {
        const visible = password.substring(0, visibleChars)
        const hidden = '•'.repeat(password.length - visibleChars)
        return visible + hidden
    }

    return '•'.repeat(password.length)
}

/**
 * 检查密码是否为常见弱密码
 * @param {string} password 密码
 * @returns {boolean} 是否为弱密码
 */
export function isWeakPassword(password) {
    if (!password) return true

    const weakPatterns = [
        /^123+$/,           // 纯数字序列
        /^abc+$/i,          // 纯字母序列
        /^(.)\1+$/,         // 重复字符
        /^password/i,       // 包含password
        /^admin/i,          // 包含admin
        /^root/i,           // 包含root
        /^qwerty/i,         // 键盘序列
        /^12345/,           // 数字序列
        /^abcde/i           // 字母序列
    ]

    return weakPatterns.some(pattern => pattern.test(password)) || password.length < 6
}

/**
 * 生成密码历史记录
 * @param {string} newPassword 新密码
 * @param {array} history 历史记录
 * @param {number} maxHistory 最大历史记录数
 * @returns {array} 更新后的历史记录
 */
export function updatePasswordHistory(newPassword, history = [], maxHistory = 5) {
    const newRecord = {
        password: newPassword,
        timestamp: new Date().toISOString(),
        id: Date.now()
    }

    const updatedHistory = [newRecord, ...history]
    return updatedHistory.slice(0, maxHistory)
}

/**
 * 检查密码是否在历史记录中
 * @param {string} password 密码
 * @param {array} history 历史记录
 * @returns {boolean} 是否存在于历史记录中
 */
export function isPasswordInHistory(password, history = []) {
    return history.some(record => record.password === password)
}

/**
 * 生成密码复杂度报告
 * @param {string} password 密码
 * @returns {object} 复杂度报告
 */
export function generatePasswordComplexityReport(password) {
    if (!password) {
        return {
            score: 0,
            maxScore: 100,
            details: {
                length: { score: 0, max: 25, description: '长度' },
                variety: { score: 0, max: 25, description: '字符多样性' },
                uniqueness: { score: 0, max: 25, description: '唯一性' },
                patterns: { score: 0, max: 25, description: '模式复杂度' }
            }
        }
    }

    const details = {
        length: { score: 0, max: 25, description: '长度' },
        variety: { score: 0, max: 25, description: '字符多样性' },
        uniqueness: { score: 0, max: 25, description: '唯一性' },
        patterns: { score: 0, max: 25, description: '模式复杂度' }
    }

    // 长度评分
    if (password.length >= 8) details.length.score += 10
    if (password.length >= 12) details.length.score += 10
    if (password.length >= 16) details.length.score += 5

    // 字符多样性评分
    if (/[a-z]/.test(password)) details.variety.score += 6
    if (/[A-Z]/.test(password)) details.variety.score += 6
    if (/\d/.test(password)) details.variety.score += 6
    if (/[^A-Za-z0-9]/.test(password)) details.variety.score += 7

    // 唯一性评分（避免常见密码）
    if (!isWeakPassword(password)) details.uniqueness.score += 25

    // 模式复杂度评分
    if (!/(.)\1{2,}/.test(password)) details.patterns.score += 8  // 无重复字符
    if (!/123|abc|qwe/i.test(password)) details.patterns.score += 8  // 无序列
    if (password.length > 0) details.patterns.score += Math.min(9, password.length)  // 基础分

    const totalScore = Object.values(details).reduce((sum, item) => sum + item.score, 0)

    return {
        score: totalScore,
        maxScore: 100,
        details
    }
}