<template>
  <div class="space-y-6">
    <!-- 操作工具栏 -->
    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- 主要操作按钮 -->
        <div class="flex flex-wrap gap-3">
          <button
            class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
            @click="showEmergencyReset">
            <font-awesome-icon :icon="['fas', 'exclamation-triangle']" class="mr-2" />
            <span>紧急重置</span>
          </button>
          <button
            class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
            @click="openBatchUpdateModal">
            <font-awesome-icon :icon="['fas', 'key']" class="mr-2" />
            <span>批量更新密码</span>
          </button>
          <button
            class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
            @click="openBatchApplyModal">
            <font-awesome-icon :icon="['fas', 'shield-alt']" class="mr-2" />
            <span>批量应用策略</span>
          </button>
          <button
            class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
            @click="openBatchAddAccountModal">
            <font-awesome-icon :icon="['fas', 'users']" class="mr-2" />
            <span>批量添加账号</span>
          </button>
        </div>

        <!-- 筛选和视图控制 -->
        <div class="flex flex-wrap items-center gap-3">
          <!-- 视图切换 -->
          <div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              class="px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none"
              :class="viewMode === 'table'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'"
              @click="viewMode = 'table'">
              <font-awesome-icon :icon="['fas', 'table']" class="mr-1.5" />
              表格
            </button>
            <button
              class="px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none"
              :class="viewMode === 'card'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'"
              @click="viewMode = 'card'">
              <font-awesome-icon :icon="['fas', 'th-large']" class="mr-1.5" />
              卡片
            </button>
          </div>

          <!-- 搜索框 -->
          <div class="relative">
            <input
              type="text"
              v-model="filterText"
              placeholder="搜索主机..."
              class="w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <font-awesome-icon :icon="['fas', 'search']" class="text-gray-400" />
            </div>
          </div>

          <!-- 账号筛选 -->
          <div class="relative">
            <input
              type="text"
              v-model="accountFilterText"
              placeholder="搜索账号..."
              class="w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <font-awesome-icon :icon="['fas', 'user']" class="text-gray-400" />
            </div>
          </div>

          <!-- 筛选下拉菜单 -->
          <div class="flex gap-2">
            <select
              v-model="statusFilter"
              class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            >
              <option value="all">所有状态</option>
              <option value="normal">正常</option>
              <option value="warning">警告</option>
              <option value="error">错误</option>
            </select>

            <select
              v-model="expiryFilter"
              class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            >
              <option value="all">所有密码</option>
              <option value="expired">已过期</option>
              <option value="expiring-soon">即将过期</option>
              <option value="valid">有效期内</option>
            </select>

            <select
              v-model="policyFilter"
              class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            >
              <option value="all">所有策略</option>
              <option v-for="policy in policies" :key="policy.id" :value="policy.id">
                {{ policy.name }}
              </option>
              <option value="none">无策略</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 主机列表 -->
    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 账号计数和导出按钮 -->
      <div class="px-4 py-3 bg-gray-50 border-b flex justify-between items-center">
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ filteredAccounts.length }}</span> 个账号
          (共 <span class="font-medium">{{ getAllAccounts.length }}</span> 个)
        </div>
        <div class="flex space-x-2">
          <button
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="exportPasswordsToCSV">
            <font-awesome-icon :icon="['fas', 'file-export']" class="mr-1" />
            导出
          </button>
          <button
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="printPasswordData">
            <font-awesome-icon :icon="['fas', 'print']" class="mr-1" />
            打印
          </button>
        </div>
      </div>

      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <CustomCheckbox v-model="selectAll" @update:modelValue="toggleSelectAll">
                主机名
              </CustomCheckbox>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              IP地址
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              账号
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              最后密码修改时间
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              密码过期时间
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              密码
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              策略
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <!-- 按主机分组显示 -->
          <template v-for="hostGroup in groupedAccounts" :key="hostGroup.hostId">
            <!-- 账号行 -->
            <tr v-for="(account, accountIndex) in hostGroup.accounts" :key="account.id"
              :class="{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <CustomCheckbox v-model="account.host.selected" class="ml-4">
                    <span class="ml-2 font-medium text-gray-900">{{ account.host.name }}</span>
                  </CustomCheckbox>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ account.host.ip }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span class="text-sm font-medium text-gray-900">{{ account.username }}</span>
                  <span v-if="account.isDefault"
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    默认
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ account.lastPasswordChange || '-' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div :class="{
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,
                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',
                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',
                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'
                }">
                  {{ isPasswordExpired(account).text }}
                  <span
                    v-if="isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'"
                    class="ml-1">
                    <font-awesome-icon :icon="['fas', 'exclamation-triangle']" />
                  </span>
                  <span v-else-if="isPasswordExpired(account).status === 'warning'" class="ml-1">
                    <font-awesome-icon :icon="['fas', 'exclamation-circle']" />
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-grow">
                    <input :type="passwordVisibility[account.id] ? 'text' : 'password'" :value="account.password"
                      readonly
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5" />
                  </div>
                  <button @click="togglePasswordVisibility(account.id)"
                    class="ml-2 text-gray-600 hover:text-blue-700 focus:outline-none">
                    <font-awesome-icon :icon="['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']"
                      class="text-lg" />
                  </button>
                  <div class="ml-2" v-tooltip="isPasswordCompliant(account).text">
                    <font-awesome-icon 
                      :icon="['fas', isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle']" 
                      :class="isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500'" 
                    />
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <StatusBadge :type="account.host.status" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div :class="['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]">
                  {{ getPolicyName(account.policyId) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div class="flex space-x-2">
                  <button
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    @click="openChangePasswordModal(account.host, account)">
                    <font-awesome-icon :icon="['fas', 'key']" class="mr-1" />
                    修改密码
                  </button>
                  <button
                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    @click="copyPassword(account)">
                    <font-awesome-icon :icon="['fas', 'copy']" class="mr-1" />
                    复制
                  </button>
                </div>
              </td>
            </tr>
          </template>
          <!-- 无数据显示 -->
          <tr v-if="filteredAccounts.length === 0">
            <td colspan="9" class="px-6 py-10 text-center">
              <div class="text-gray-500">
                <font-awesome-icon :icon="['fas', 'search']" class="text-4xl mb-3" />
                <p>没有找到匹配的账号数据</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
      <div v-for="host in filteredHosts" :key="host.id" class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <!-- 主机头部 -->
          <div class="flex flex-wrap justify-between items-start mb-4">
            <div class="flex items-center mb-2 sm:mb-0">
              <CustomCheckbox v-model="host.selected" class="mr-2" />
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ host.name }}</h3>
                <p class="text-sm text-gray-500">{{ host.ip }}</p>
              </div>
            </div>
            <StatusBadge :type="host.status" />
          </div>

          <!-- 账号列表 -->
          <div class="space-y-4">
            <div v-for="account in host.accounts" :key="account.id" class="border border-gray-200 rounded-lg p-3"
              :class="{ 'border-green-300 bg-green-50': account.isDefault }">
              <div class="flex flex-wrap justify-between items-center mb-2">
                <div class="flex items-center mb-2 sm:mb-0">
                  <span class="text-sm font-medium text-gray-900">{{ account.username }}</span>
                  <span v-if="account.isDefault"
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    默认
                  </span>
                </div>
                <button
                  class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  @click="openChangePasswordModal(host, account)">
                  <font-awesome-icon :icon="['fas', 'key']" class="mr-1" />
                  修改密码
                </button>
              </div>

              <!-- 密码展示 -->
              <div class="mb-2">
                <div class="text-xs font-medium text-gray-500 mb-1">密码</div>
                <div class="flex items-center">
                  <input :type="passwordVisibility[account.id] ? 'text' : 'password'" :value="account.password" readonly
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5" />
                  <button @click="togglePasswordVisibility(account.id)"
                    class="ml-2 text-gray-600 hover:text-blue-700 focus:outline-none">
                    <font-awesome-icon :icon="['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']"
                      class="text-lg" />
                  </button>
                  <div class="ml-2" v-tooltip="isPasswordCompliant(account).text">
                    <font-awesome-icon 
                      :icon="['fas', isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle']" 
                      :class="isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500'" 
                    />
                  </div>
                </div>
              </div>

              <!-- 密码信息区域 -->
              <div class="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <div class="font-medium text-gray-500 mb-1">最后修改时间</div>
                  <div class="text-gray-900">{{ account.lastPasswordChange || '-' }}</div>
                </div>
                <div>
                  <div class="font-medium text-gray-500 mb-1">密码过期</div>
                  <div :class="{
                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,
                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',
                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',
                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'
                  }">
                    {{ isPasswordExpired(account).text }}
                    <span
                      v-if="isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'"
                      class="ml-1">
                      <font-awesome-icon :icon="['fas', 'exclamation-triangle']" />
                    </span>
                  </div>
                </div>
                <div class="col-span-2 mt-1">
                  <div class="font-medium text-gray-500 mb-1">密码策略</div>
                  <div :class="['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]">
                    {{ getPolicyName(account.policyId) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 添加账号按钮 -->
          <div class="mt-4 flex justify-center">
            <button
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              @click="openAddAccountModal(host)">
              <font-awesome-icon :icon="['fas', 'plus']" class="mr-1" />
              添加账号
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <BaseModal v-model="changePasswordModal.show" title="修改密码" size="lg" @confirm="updatePassword" :loading="processing">
      <!-- 主机信息卡片 -->
      <div class="mb-6">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <font-awesome-icon :icon="['fas', 'server']" class="text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white">{{ currentHost.name }}</h4>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                <span>{{ currentHost.ip }}</span> • <span>账号: {{ currentAccount.username }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 密码生成方式选择 -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">密码生成方式</label>
        <div class="grid grid-cols-2 gap-3">
          <button
            @click="changePasswordModal.method = 'auto'"
            class="flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none"
            :class="changePasswordModal.method === 'auto'
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'"
          >
            <font-awesome-icon :icon="['fas', 'magic']" class="mr-2" />
            <span class="font-medium">智能生成</span>
          </button>
          <button
            @click="changePasswordModal.method = 'manual'"
            class="flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none"
            :class="changePasswordModal.method === 'manual'
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'"
          >
            <font-awesome-icon :icon="['fas', 'edit']" class="mr-2" />
            <span class="font-medium">手动输入</span>
          </button>
        </div>
      </div>

      <!-- 智能生成模式 -->
      <div v-if="changePasswordModal.method === 'auto'" class="mb-6">
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <AdvancedPasswordGenerator
            :initial-options="getPasswordGeneratorOptions()"
            @password-generated="onPasswordGenerated"
          />
        </div>
      </div>

      <!-- 手动输入模式 -->
      <div v-else class="mb-6">
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">新密码</label>
            <div class="relative">
              <input
                :type="passwordVisibility.new ? 'text' : 'password'"
                v-model="changePasswordModal.newPassword"
                class="w-full px-4 py-3 pr-12 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
                placeholder="输入新密码"
                @input="validatePassword"
              />
              <button
                @click="passwordVisibility.new = !passwordVisibility.new"
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                <font-awesome-icon :icon="['fas', passwordVisibility.new ? 'eye-slash' : 'eye']" />
              </button>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">确认密码</label>
            <div class="relative">
              <input
                :type="passwordVisibility.confirm ? 'text' : 'password'"
                v-model="changePasswordModal.confirmPassword"
                class="w-full px-4 py-3 pr-12 bg-white dark:bg-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
                :class="passwordMismatch
                  ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300 dark:border-gray-600'"
                placeholder="再次输入新密码"
                @input="validatePassword"
              />
              <button
                @click="passwordVisibility.confirm = !passwordVisibility.confirm"
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                <font-awesome-icon :icon="['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']" />
              </button>
            </div>
            <div v-if="passwordMismatch" class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
              <font-awesome-icon :icon="['fas', 'exclamation-circle']" class="mr-1" />
              两次输入的密码不一致
            </div>
          </div>

          <!-- 密码强度指示器 -->
          <div v-if="changePasswordModal.newPassword">
            <PasswordStrengthMeter :password="changePasswordModal.newPassword" />
          </div>
        </div>
      </div>

      <!-- 执行选项 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
          <font-awesome-icon :icon="['fas', 'cogs']" class="mr-2 text-blue-500" />
          执行选项
        </h4>
        <div class="space-y-3">
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              v-model="changePasswordModal.executeImmediately"
              class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
            />
            <div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">立即执行</span>
              <p class="text-xs text-gray-500 dark:text-gray-400">密码更改后立即应用到目标主机</p>
            </div>
          </label>

          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              v-model="changePasswordModal.saveHistory"
              class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
            />
            <div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">保存历史记录</span>
              <p class="text-xs text-gray-500 dark:text-gray-400">将旧密码保存到历史记录中</p>
            </div>
          </label>

          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              v-model="changePasswordModal.logAudit"
              class="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
            />
            <div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">记录审计日志</span>
              <p class="text-xs text-gray-500 dark:text-gray-400">在系统日志中记录此次密码更改操作</p>
            </div>
          </label>
        </div>
      </div>
    </BaseModal>

    <!-- 添加账号弹窗 -->
    <BaseModal v-model="addAccountModal.show" title="添加账号" @confirm="addAccount" :loading="processing">
      <div class="mb-4">
        <div class="font-medium mb-2">主机信息</div>
        <div class="px-3 py-2 bg-gray-50 rounded-md">
          <div><span class="font-medium">主机名:</span> {{ currentHost.name }}</div>
          <div><span class="font-medium">IP地址:</span> {{ currentHost.ip }}</div>
        </div>
      </div>

      <div class="form-group mb-4">
        <label class="form-label">账号名称</label>
        <input type="text" v-model="addAccountModal.username" class="form-control" placeholder="输入账号名称" />
      </div>

      <div class="form-group mb-4">
        <label class="form-label">设为默认账号</label>
        <div class="relative inline-block w-10 mr-2 align-middle select-none">
          <input type="checkbox" v-model="addAccountModal.isDefault"
            class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none" />
          <label class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></label>
        </div>
      </div>

      <div class="form-group mb-4">
        <label class="form-label">密码策略</label>
        <select v-model="addAccountModal.policyId" class="form-select" @change="generatePasswordForNewAccount()">
          <option v-for="policy in policies" :key="policy.id" :value="policy.id">
            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)
          </option>
        </select>
      </div>

      <div class="form-group">
        <div class="flex justify-between mb-1">
          <label class="form-label">生成的密码</label>
          <button @click="generatePasswordForNewAccount()" type="button"
            class="text-xs text-blue-600 hover:text-blue-800 focus:outline-none">
            <font-awesome-icon :icon="['fas', 'sync-alt']" class="mr-1" />
            重新生成
          </button>
        </div>
        <div class="flex items-center">
          <input :type="passwordVisibility.newAccount ? 'text' : 'password'" v-model="addAccountModal.password" readonly
            class="form-control flex-1 bg-gray-50" />
          <button @click="passwordVisibility.newAccount = !passwordVisibility.newAccount" type="button"
            class="ml-2 text-gray-600 hover:text-blue-700 focus:outline-none">
            <font-awesome-icon :icon="['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']" class="text-lg" />
          </button>
        </div>
      </div>
    </BaseModal>

    <!-- 批量更新密码弹窗 -->
    <BaseModal v-model="batchUpdateModal.show" title="批量更新密码" confirm-text="开始更新" size="lg"
      @confirm="batchUpdatePasswords" :loading="processing">
      <div class="form-group">
        <label class="form-label">选择目标主机</label>
        <div class="mb-2">
          <CustomCheckbox v-model="selectAllBatch" @update:modelValue="toggleSelectAllBatch">
            全选
          </CustomCheckbox>
        </div>
        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
          <CustomCheckbox v-for="host in hosts" :key="host.id" v-model="batchUpdateModal.selectedHosts[host.id]">
            {{ host.name }} ({{ host.ip }})
          </CustomCheckbox>
        </div>
        <p class="form-text">已选择 {{ selectedHostsCount }} 台主机</p>
      </div>

      <div class="form-group">
        <label class="form-label">密码策略</label>
        <select v-model="batchUpdateModal.policyId" class="form-select">
          <option v-for="policy in policies" :key="policy.id" :value="policy.id">
            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)
          </option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">执行时间</label>
        <div class="flex space-x-4">
          <label class="flex items-center">
            <input type="radio" v-model="batchUpdateModal.executionTime" value="immediate" class="mr-2">
            <span>立即执行</span>
          </label>
          <label class="flex items-center">
            <input type="radio" v-model="batchUpdateModal.executionTime" value="scheduled" class="mr-2">
            <span>定时执行</span>
          </label>
        </div>

        <div v-if="batchUpdateModal.executionTime === 'scheduled'" class="mt-3">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="form-label">日期</label>
              <input type="date" v-model="batchUpdateModal.scheduledDate" class="form-control">
            </div>
            <div>
              <label class="form-label">时间</label>
              <input type="time" v-model="batchUpdateModal.scheduledTime" class="form-control">
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">高级选项</label>
        <CustomCheckbox v-model="batchUpdateModal.ignoreErrors">
          忽略错误继续执行
        </CustomCheckbox>
        <CustomCheckbox v-model="batchUpdateModal.detailedLog">
          记录详细日志
        </CustomCheckbox>
        <CustomCheckbox v-model="batchUpdateModal.sendNotification">
          执行完成后发送通知
        </CustomCheckbox>
      </div>
    </BaseModal>

    <!-- 批量应用策略弹窗 -->
    <BaseModal v-model="batchApplyModal.show" title="批量应用密码策略" confirm-text="应用策略" @confirm="batchApplyPolicy"
      :loading="processing">
      <div class="form-group">
        <label class="form-label">选择目标主机</label>
        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
          <CustomCheckbox v-for="host in selectedHostsList" :key="host.id"
            v-model="batchApplyModal.selectedHosts[host.id]">
            {{ host.name }} ({{ host.ip }})
          </CustomCheckbox>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">选择密码策略</label>
        <select v-model="batchApplyModal.policyId" class="form-select">
          <option v-for="policy in policies" :key="policy.id" :value="policy.id">
            {{ policy.name }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">应用选项</label>
        <CustomCheckbox v-model="batchApplyModal.updateImmediately">
          立即更新密码以符合策略
        </CustomCheckbox>
        <CustomCheckbox v-model="batchApplyModal.applyOnNextUpdate">
          下次密码更新时应用
        </CustomCheckbox>
      </div>
    </BaseModal>

    <!-- 紧急重置密码弹窗 -->
    <BaseModal v-model="emergencyResetModal.show" title="紧急密码重置" confirm-text="立即重置" icon="exclamation-triangle" danger
      @confirm="emergencyReset" :loading="processing">
      <div class="bg-red-50 text-red-700 p-3 rounded-md mb-4">
        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>
      </div>

      <div class="form-group">
        <label class="form-label">选择目标主机</label>
        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
          <CustomCheckbox v-for="host in selectedHostsList" :key="host.id"
            v-model="emergencyResetModal.selectedHosts[host.id]">
            {{ host.name }} ({{ host.ip }})
          </CustomCheckbox>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">应用紧急策略</label>
        <select v-model="emergencyResetModal.policyId" class="form-select">
          <option v-for="policy in emergencyPolicies" :key="policy.id" :value="policy.id">
            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)
          </option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">操作原因</label>
        <select v-model="emergencyResetModal.reason" class="form-select">
          <option value="security_incident">安全事件响应</option>
          <option value="password_leak">密码泄露</option>
          <option value="abnormal_access">异常访问</option>
          <option value="compliance">合规要求</option>
          <option value="other">其他原因</option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">附加说明</label>
        <textarea v-model="emergencyResetModal.description" class="form-control" rows="2"
          placeholder="请输入重置原因详细说明"></textarea>
      </div>
    </BaseModal>

    <!-- 批量添加账号弹窗 -->
    <BaseModal v-model="batchAddAccountModal.show" title="批量添加账号" size="lg" @confirm="batchAddAccounts"
      :loading="processing">
      <div class="form-group mb-4">
        <label class="form-label">选择目标主机</label>
        <div class="mb-2">
          <CustomCheckbox v-model="selectAllBatchAdd" @update:modelValue="toggleSelectAllBatchAdd">
            全选
          </CustomCheckbox>
        </div>
        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
          <CustomCheckbox v-for="host in hosts" :key="host.id" v-model="batchAddAccountModal.selectedHosts[host.id]">
            {{ host.name }} ({{ host.ip }})
          </CustomCheckbox>
        </div>
        <p class="form-text">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>
      </div>

      <div class="form-group mb-4">
        <label class="form-label">账号信息</label>
        <div class="p-4 border border-gray-200 rounded-md">
          <div class="mb-3">
            <label class="form-label">账号名称 <span class="text-red-500">*</span></label>
            <input type="text" v-model="batchAddAccountModal.username" class="form-control" placeholder="输入统一账号名称" />
            <div class="text-xs text-gray-500 mt-1">将在所有选中主机上创建同名账号</div>
          </div>

          <div class="mb-3">
            <label class="form-label">账号角色</label>
            <select v-model="batchAddAccountModal.role" class="form-select">
              <option value="admin">管理员</option>
              <option value="user">普通用户</option>
              <option value="service">服务账号</option>
              <option value="readonly">只读账号</option>
            </select>
          </div>

          <div class="flex items-center mb-3">
            <label class="inline-flex items-center">
              <input type="checkbox" v-model="batchAddAccountModal.setAsDefault" class="form-checkbox">
              <span class="ml-2">设为默认账号</span>
            </label>
          </div>

          <div class="mb-3">
            <label class="form-label">密码生成方式</label>
            <div class="flex space-x-3">
              <button @click="batchAddAccountModal.useSamePassword = true"
                class="flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors"
                :class="batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'">
                <font-awesome-icon :icon="['fas', 'key']" class="mr-2" />
                同一密码
              </button>
              <button @click="batchAddAccountModal.useSamePassword = false"
                class="flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors"
                :class="!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'">
                <font-awesome-icon :icon="['fas', 'random']" class="mr-2" />
                随机密码
              </button>
            </div>
          </div>

          <div v-if="batchAddAccountModal.useSamePassword" class="mb-3">
            <label class="form-label">统一密码</label>
            <div class="flex items-center">
              <input :type="passwordVisibility.batchPassword ? 'text' : 'password'"
                v-model="batchAddAccountModal.password" readonly class="form-control flex-1 bg-gray-50" />
              <button @click="passwordVisibility.batchPassword = !passwordVisibility.batchPassword" type="button"
                class="ml-2 text-gray-600 hover:text-blue-700 focus:outline-none">
                <font-awesome-icon :icon="['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']"
                  class="text-lg" />
              </button>
              <button @click="generatePasswordForBatchAccount()" type="button"
                class="ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md">
                <font-awesome-icon :icon="['fas', 'sync-alt']" class="mr-1" />
                重新生成
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group mb-4">
        <label class="form-label">密码策略</label>
        <select v-model="batchAddAccountModal.policyId" class="form-select" @change="generatePasswordForBatchAccount()">
          <option v-for="policy in policies" :key="policy.id" :value="policy.id">
            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)
          </option>
        </select>
      </div>

      <div class="form-group mb-4">
        <label class="form-label">高级选项</label>
        <div class="space-y-2">
          <CustomCheckbox v-model="batchAddAccountModal.ignoreErrors">
            <span class="ml-2">忽略错误继续执行</span>
          </CustomCheckbox>
          <CustomCheckbox v-model="batchAddAccountModal.generateReport">
            <span class="ml-2">生成账号创建报告</span>
          </CustomCheckbox>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import BaseModal from '@/components/BaseModal.vue'
import StatusBadge from '@/components/StatusBadge.vue'
import CustomCheckbox from '@/components/CustomCheckbox.vue'
import PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'
import AdvancedPasswordGenerator from '@/components/AdvancedPasswordGenerator.vue'
import { showSuccess, showError, showWarning } from '@/utils/notification.js'
import { validateField, validateForm, ValidationSchemas } from '@/utils/validation'
import { handleError, ErrorTypes, ErrorLevels } from '@/utils/errorHandler'
import { calculatePasswordExpiry, validatePasswordPolicy } from '@/utils/passwordUtils'
import { sanitizeInput, escapeHtml } from '@/utils/security'

export default {
  name: 'HostManagement',
  components: {
    BaseModal,
    StatusBadge,
    CustomCheckbox,
    PasswordStrengthMeter,
    AdvancedPasswordGenerator
  },
  data() {
    return {
      selectAll: false,
      selectAllBatch: false,
      selectAllBatchAdd: false,
      processing: false,
      currentHost: {},
      currentAccount: {},
      passwordVisibility: {
        generated: false,
        new: false,
        confirm: false,
        newAccount: false,
        batchPassword: false
      },
      viewMode: 'table',
      filterText: '',
      accountFilterText: '',
      statusFilter: 'all',
      expiryFilter: 'all',
      policyFilter: 'all',

      // 修改密码弹窗
      changePasswordModal: {
        show: false,
        method: 'auto',
        policyId: 1,
        generatedPassword: 'aX7#9pQr$2Lm',
        newPassword: '',
        confirmPassword: '',
        executeImmediately: true,
        saveHistory: false,
        logAudit: true
      },

      // 批量更新密码弹窗
      batchUpdateModal: {
        show: false,
        selectedHosts: {},
        policyId: 1,
        executionTime: 'immediate',
        scheduledDate: '',
        scheduledTime: '',
        ignoreErrors: true,
        detailedLog: true,
        sendNotification: false
      },

      // 批量应用策略弹窗
      batchApplyModal: {
        show: false,
        selectedHosts: {},
        policyId: 1,
        updateImmediately: false,
        applyOnNextUpdate: true
      },

      // 紧急重置密码弹窗
      emergencyResetModal: {
        show: false,
        selectedHosts: {},
        policyId: 3, // 默认使用紧急策略
        reason: 'security_incident',
        description: ''
      },

      // 添加账号弹窗
      addAccountModal: {
        show: false,
        username: '',
        password: '',
        isDefault: false,
        policyId: 1
      },

      // 批量添加账号弹窗
      batchAddAccountModal: {
        show: false,
        selectedHosts: {},
        username: '',
        password: '',
        role: 'admin',
        setAsDefault: false,
        useSamePassword: true,
        policyId: 1
      }
    }
  },
  computed: {
    ...mapState({
      hosts: state => state.hosts,
      policies: state => state.policies
    }),
    ...mapGetters(['selectedHosts']),

    // 获取策略名称
    getPolicyName() {
      return (policyId) => {
        if (!policyId) return '无';
        const policy = this.policies.find(p => p.id === policyId);
        return policy ? policy.name : '无';
      };
    },

    // 获取策略颜色类
    getPolicyColorClass() {
      return (policyId) => {
        if (!policyId) return 'bg-gray-100 text-gray-800';
        
        // 根据策略ID返回对应的颜色类
        const colorMap = {
          1: 'bg-red-100 text-red-800',    // 高强度策略
          2: 'bg-blue-100 text-blue-800',  // 标准策略
          3: 'bg-purple-100 text-purple-800' // 紧急策略
        };
        
        return colorMap[policyId] || 'bg-gray-100 text-gray-800';
      };
    },

    passwordMismatch() {
      return this.changePasswordModal.newPassword &&
        this.changePasswordModal.confirmPassword &&
        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword
    },

    selectedHostsCount() {
      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length
    },

    selectedHostsList() {
      return this.hosts.filter(host => host.selected)
    },

    emergencyPolicies() {
      // 返回紧急策略和高强度策略
      return this.policies.filter(p => p.id === 3 || p.id === 1)
    },

    // 过滤后的主机列表
    filteredHosts() {
      return this.hosts.filter(host => {
        // 文本过滤
        const textMatch = this.filterText === '' ||
          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||
          host.ip.includes(this.filterText);

        // 状态过滤
        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;

        return textMatch && statusMatch;
      });
    },

    // 获取所有账号（扁平化处理）
    getAllAccounts() {
      // 为每个账号添加主机引用
      const accounts = [];
      this.filteredHosts.forEach(host => {
        host.accounts.forEach(account => {
          accounts.push({
            ...account,
            host: host
          });
        });
      });
      return accounts;
    },

    // 筛选后的账号
    filteredAccounts() {
      return this.getAllAccounts.filter(account => {
        // 账号名称筛选
        const accountMatch = this.accountFilterText === '' ||
          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());

        // 密码过期筛选
        let expiryMatch = true;
        if (this.expiryFilter !== 'all') {
          const expiryStatus = this.isPasswordExpired(account).status;
          if (this.expiryFilter === 'expired') {
            expiryMatch = expiryStatus === 'expired';
          } else if (this.expiryFilter === 'expiring-soon') {
            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';
          } else if (this.expiryFilter === 'valid') {
            expiryMatch = expiryStatus === 'normal';
          }
        }

        // 策略筛选
        let policyMatch = true;
        if (this.policyFilter !== 'all') {
          if (this.policyFilter === 'none') {
            policyMatch = !account.policyId;
          } else {
            policyMatch = account.policyId === parseInt(this.policyFilter);
          }
        }

        return accountMatch && expiryMatch && policyMatch;
      });
    },

    // 分组后的账号列表
    groupedAccounts() {
      // 按主机ID分组
      const groups = {};
      this.filteredAccounts.forEach(account => {
        const hostId = account.host.id;
        if (!groups[hostId]) {
          groups[hostId] = {
            hostId: hostId,
            hostName: account.host.name,
            hostIp: account.host.ip,
            host: account.host,
            accounts: []
          };
        }
        groups[hostId].accounts.push(account);
      });

      // 转换为数组
      return Object.values(groups);
    },

    selectedBatchAddHostsCount() {
      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length
    }
  },
  methods: {
    toggleSelectAll(value) {
      this.$store.commit('selectAllHosts', value)
    },

    toggleSelectAllBatch(value) {
      this.hosts.forEach(host => {
        this.batchUpdateModal.selectedHosts[host.id] = value
      })
    },

    toggleSelectAllBatchAdd(value) {
      this.hosts.forEach(host => {
        this.batchAddAccountModal.selectedHosts[host.id] = value
      })
    },

    openChangePasswordModal(host, account) {
      this.currentHost = host
      this.currentAccount = account
      this.changePasswordModal.show = true
      this.changePasswordModal.generatedPassword = this.generatePassword()
    },

    openBatchUpdateModal() {
      this.batchUpdateModal.show = true

      // 初始化选中状态
      this.hosts.forEach(host => {
        this.batchUpdateModal.selectedHosts[host.id] = host.selected
      })

      // 设置默认值
      const today = new Date()
      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]
      this.batchUpdateModal.scheduledTime = '03:00'
    },

    openBatchApplyModal() {
      this.batchApplyModal.show = true

      // 初始化选中状态
      this.hosts.forEach(host => {
        this.batchApplyModal.selectedHosts[host.id] = host.selected
      })
    },

    showEmergencyReset() {
      this.emergencyResetModal.show = true

      // 初始化选中状态
      this.hosts.forEach(host => {
        this.emergencyResetModal.selectedHosts[host.id] = host.selected
      })
    },

    generatePassword(policy) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'
      let password = ''

      // 获取所选策略的最小长度
      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)
      const minLength = policyObj ? policyObj.minLength : 12

      // 生成随机密码
      for (let i = 0; i < minLength; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length))
      }

      if (this.changePasswordModal && !policy) {
        this.changePasswordModal.generatedPassword = password
      }

      return password
    },

    async updatePassword() {
      try {
        // 验证输入
        if (this.changePasswordModal.method === 'manual') {
          if (this.passwordMismatch) {
            showError('两次输入的密码不一致，请重新输入')
            return
          }

          if (!this.changePasswordModal.newPassword) {
            showError('请输入新密码')
            return
          }

          // 验证密码强度和策略合规性
          const password = this.changePasswordModal.newPassword
          const policy = this.policies.find(p => p.id === this.changePasswordModal.policyId)

          if (policy) {
            const validation = validatePasswordPolicy(password, policy)
            if (!validation.valid) {
              showError(`密码不符合策略要求：${validation.errors.join(', ')}`)
              return
            }
          }

          // 安全检查
          const sanitizedPassword = sanitizeInput(password, {
            maxLength: 128,
            allowSpecialChars: true
          })

          if (sanitizedPassword !== password) {
            showError('密码包含不安全的字符')
            return
          }
        }

        this.processing = true

        const password = this.changePasswordModal.method === 'auto'
          ? this.changePasswordModal.generatedPassword
          : this.changePasswordModal.newPassword

        await this.$store.dispatch('updateHostPassword', {
          hostId: this.currentHost.id,
          accountId: this.currentAccount.id,
          password: password,
          policyId: this.changePasswordModal.policyId
        })

        this.changePasswordModal.show = false
        this.resetPasswordModal()

        showSuccess(
          `主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码已成功更新`,
          '密码更新成功'
        )

      } catch (error) {
        handleError(error, ErrorTypes.SERVER, ErrorLevels.HIGH, {
          component: 'HostManagement',
          method: 'updatePassword',
          hostId: this.currentHost?.id,
          accountId: this.currentAccount?.id
        })
        showError('密码更新失败，请重试')
      } finally {
        this.processing = false
      }
    },

    // 重置密码弹窗状态
    resetPasswordModal() {
      this.changePasswordModal.newPassword = ''
      this.changePasswordModal.confirmPassword = ''
      this.changePasswordModal.generatedPassword = ''
      this.changePasswordModal.method = 'auto'
      this.passwordVisibility.new = false
      this.passwordVisibility.confirm = false
    },

    // 新增方法：获取密码生成器选项
    getPasswordGeneratorOptions() {
      const policy = this.policies.find(p => p.id === this.changePasswordModal.policyId) || this.policies[0]
      return {
        length: policy.minLength || 12,
        includeUppercase: policy.requireUppercase !== false,
        includeLowercase: policy.requireLowercase !== false,
        includeNumbers: policy.requireNumbers !== false,
        includeSymbols: policy.requireSpecial !== false
      }
    },

    // 新增方法：处理密码生成事件
    onPasswordGenerated(password) {
      this.changePasswordModal.generatedPassword = password
    },

    // 新增方法：验证密码
    validatePassword() {
      // 这里可以添加实时密码验证逻辑
      // 例如检查密码强度、策略合规性等
    },

    async batchUpdatePasswords() {
      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)
        .filter(([_, selected]) => selected)
        .map(([id]) => parseInt(id))

      if (selectedHostIds.length === 0) {
        alert('请至少选择一台主机！')
        return
      }

      if (this.batchUpdateModal.executionTime === 'scheduled') {
        // 在实际应用中，这里会创建一个定时任务
        alert('已创建定时密码更新任务！')
        this.batchUpdateModal.show = false
        return
      }

      this.processing = true

      try {
        // 获取所选策略
        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)

        // 为每台主机的每个账号更新密码
        for (const hostId of selectedHostIds) {
          const host = this.hosts.find(h => h.id === hostId)
          if (host) {
            for (const account of host.accounts) {
              const newPassword = this.generatePassword(policy)
              await this.$store.dispatch('updateHostPassword', {
                hostId: hostId,
                accountId: account.id,
                password: newPassword,
                policyId: policy.id
              })
            }
          }
        }

        this.batchUpdateModal.show = false

        // 提示用户操作成功
        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)
      } catch (error) {
        console.error('批量更新密码失败', error)
        alert('批量更新密码失败，请重试！')
      } finally {
        this.processing = false
      }
    },

    async batchApplyPolicy() {
      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)
        .filter(([_, selected]) => selected)
        .map(([id]) => parseInt(id))

      if (selectedHostIds.length === 0) {
        alert('请至少选择一台主机！')
        return
      }

      this.processing = true

      try {
        await this.$store.dispatch('applyPolicyToHosts', {
          policyId: this.batchApplyModal.policyId,
          hostIds: selectedHostIds
        })

        this.batchApplyModal.show = false

        // 提示用户操作成功
        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)
      } catch (error) {
        console.error('应用策略失败', error)
        alert('应用策略失败，请重试！')
      } finally {
        this.processing = false
      }
    },

    async emergencyReset() {
      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)
        .filter(([_, selected]) => selected)
        .map(([id]) => parseInt(id))

      if (selectedHostIds.length === 0) {
        alert('请至少选择一台主机！')
        return
      }

      this.processing = true

      try {
        // 获取紧急策略
        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)

        // 为每台主机的每个账号更新密码
        for (const hostId of selectedHostIds) {
          const host = this.hosts.find(h => h.id === hostId)
          if (host) {
            for (const account of host.accounts) {
              const newPassword = this.generatePassword(policy)
              await this.$store.dispatch('updateHostPassword', {
                hostId: hostId,
                accountId: account.id,
                password: newPassword,
                policyId: policy.id
              })
            }
          }
        }

        this.emergencyResetModal.show = false

        // 提示用户操作成功
        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)
      } catch (error) {
        console.error('紧急重置失败', error)
        alert('紧急重置失败，请重试！')
      } finally {
        this.processing = false
      }
    },

    togglePasswordVisibility(hostId) {
      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]
    },

    isPasswordExpired(account) {
      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }

      // 解析过期时间
      const expiryDate = new Date(account.passwordExpiryDate)
      const now = new Date()

      // 如果已过期
      if (expiryDate < now) {
        return {
          status: 'expired',
          days: 0,
          text: '已过期'
        }
      }

      // 计算剩余天数和小时数
      const diffTime = expiryDate - now
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

      // 根据剩余时间确定状态
      let status = 'normal'
      if (diffDays < 7) {
        status = 'danger'  // 少于7天
      } else if (diffDays < 14) {
        status = 'warning' // 少于14天
      }

      // 格式化显示文本
      let text = ''
      if (diffDays > 0) {
        text += `${diffDays}天`
      }
      if (diffHours > 0 || diffDays === 0) {
        text += `${diffHours}小时`
      }

      return { status, days: diffDays, text: `剩余${text}` }
    },

    openAddAccountModal(host) {
      this.currentHost = host
      this.addAccountModal.show = true
    },

    async addAccount() {
      try {
        // 验证输入
        const validation = validateForm({
          username: this.addAccountModal.username,
          password: this.addAccountModal.password
        }, ValidationSchemas.account)

        if (!validation.isValid) {
          const errors = Object.values(validation.errors).flat()
          showError(`输入验证失败：${errors.join(', ')}`)
          return
        }

        // 安全检查
        const sanitizedUsername = sanitizeInput(this.addAccountModal.username, {
          maxLength: 32,
          allowSpecialChars: false
        })

        if (sanitizedUsername !== this.addAccountModal.username) {
          showError('用户名包含不安全的字符')
          return
        }

        // 检查用户名是否已存在
        const existingAccount = this.currentHost.accounts.find(
          account => account.username === this.addAccountModal.username
        )

        if (existingAccount) {
          showError('该用户名已存在，请使用其他用户名')
          return
        }

        this.processing = true

        await this.$store.dispatch('addHostAccount', {
          hostId: this.currentHost.id,
          username: sanitizedUsername,
          password: this.addAccountModal.password,
          policyId: this.addAccountModal.policyId,
          isDefault: this.addAccountModal.isDefault
        })

        this.addAccountModal.show = false
        this.resetAddAccountModal()

        showSuccess(`已成功为主机 ${this.currentHost.name} 添加账号 ${sanitizedUsername}`)

      } catch (error) {
        handleError(error, ErrorTypes.SERVER, ErrorLevels.HIGH, {
          component: 'HostManagement',
          method: 'addAccount',
          hostId: this.currentHost?.id,
          username: this.addAccountModal.username
        })
        showError('添加账号失败，请重试')
      } finally {
        this.processing = false
      }
    },

    // 重置添加账号弹窗状态
    resetAddAccountModal() {
      this.addAccountModal.username = ''
      this.addAccountModal.password = ''
      this.addAccountModal.policyId = this.policies[0]?.id || null
      this.addAccountModal.isDefault = false
    },

    generatePasswordForNewAccount() {
      this.addAccountModal.password = this.generatePassword()
    },

    // 复制密码到剪贴板
    async copyPassword(account) {
      try {
        // 优先使用现代API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(account.password)
        } else {
          // 降级方案
          const tempInput = document.createElement('input')
          tempInput.value = account.password
          tempInput.style.position = 'fixed'
          tempInput.style.left = '-999999px'
          tempInput.style.top = '-999999px'
          document.body.appendChild(tempInput)
          tempInput.focus()
          tempInput.select()
          document.execCommand('copy')
          document.body.removeChild(tempInput)
        }

        showSuccess(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板`)
      } catch (error) {
        handleError(error, ErrorTypes.CLIENT, ErrorLevels.LOW, {
          component: 'HostManagement',
          method: 'copyPassword',
          accountId: account.id
        })
        showError('复制失败，请手动选择密码进行复制')
      }
    },

    // 导出密码数据为CSV
    exportPasswordsToCSV() {
      // 准备CSV标题行
      const headers = ['主机名', 'IP地址', '账号', '密码', '最后修改时间', '过期时间', '状态', '策略'];
      const csvRows = [headers];
      
      // 添加数据行
      this.filteredAccounts.forEach(account => {
        const row = [
          account.host.name,
          account.host.ip,
          account.username,
          account.password,
          account.lastPasswordChange || '-',
          account.passwordExpiryDate || '-',
          account.host.status,
          this.getPolicyName(account.policyId)
        ];
        csvRows.push(row);
      });
      
      // 转换为CSV格式
      const csvContent = csvRows.map(row => row.map(cell => 
        `"${String(cell).replace(/"/g, '""')}"`).join(',')).join('\n');
      
      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `密码数据_${new Date().toISOString().slice(0,10)}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    generatePasswordForBatchAccount() {
      this.batchAddAccountModal.password = this.generatePassword()
    },

    async batchAddAccounts() {
      if (!this.batchAddAccountModal.username) {
        alert('请填写账号名称！')
        return
      }

      const selectedHostIds = Object.entries(this.batchAddAccountModal.selectedHosts)
        .filter(([_, selected]) => selected)
        .map(([id]) => parseInt(id))

      if (selectedHostIds.length === 0) {
        alert('请至少选择一台主机！')
        return
      }

      this.processing = true

      try {
        await this.$store.dispatch('batchAddAccounts', {
          hostIds: selectedHostIds,
          username: this.batchAddAccountModal.username,
          password: this.batchAddAccountModal.useSamePassword ? this.batchAddAccountModal.password : null,
          role: this.batchAddAccountModal.role,
          isDefault: this.batchAddAccountModal.setAsDefault,
          policyId: this.batchAddAccountModal.policyId
        })

        this.batchAddAccountModal.show = false

        // 提示用户操作成功
        alert(`已成功为 ${selectedHostIds.length} 台主机添加账号！`)
      } catch (error) {
        console.error('批量添加账号失败', error)
        alert('批量添加账号失败，请重试！')
      } finally {
        this.processing = false
      }
    },

    openBatchAddAccountModal() {
      this.batchAddAccountModal.show = true
      this.batchAddAccountModal.selectedHosts = {}

      // 初始化选中状态
      this.hosts.forEach(host => {
        this.batchAddAccountModal.selectedHosts[host.id] = host.selected
      })

      // 生成初始密码
      this.generatePasswordForBatchAccount()
    },

    printPasswordData() {
      // 使用纯JavaScript方式创建打印内容，避免Vue模板编译问题
      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        alert("无法打开打印窗口，请允许弹出窗口");
        return;
      }
      
      const doc = printWindow.document;
      
      // 清空文档
      doc.open();
      doc.write("<!DOCTYPE html>");
      doc.write("<html>");
      doc.write("<head>");
      doc.write("<title>主机账号密码报表</title>");
      
      // 添加样式
      doc.write("<style>");
      doc.write("body { font-family: Arial, sans-serif; }");
      doc.write("table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }");
      doc.write("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
      doc.write("th { background-color: #f2f2f2; }");
      doc.write(".print-header { text-align: center; margin-bottom: 20px; }");
      doc.write(".print-footer { text-align: center; font-size: 12px; margin-top: 30px; }");
      doc.write(".status-normal { color: green; }");
      doc.write(".status-warning { color: orange; }");
      doc.write(".status-error { color: red; }");
      doc.write("@page { margin: 1cm; }");
      doc.write("</style>");
      doc.write("</head>");
      doc.write("<body>");
      
      // 添加标题
      doc.write("<div class=\"print-header\">");
      doc.write("<h1>主机账号密码报表</h1>");
      doc.write("<p>生成时间: " + new Date().toLocaleString("zh-CN") + "</p>");
      doc.write("</div>");
      
      // 添加表格
      doc.write("<table>");
      
      // 表头
      doc.write("<thead><tr>");
      ["主机名", "IP地址", "账号", "最后密码修改时间", "密码过期时间", "状态", "策略"].forEach(header => {
        doc.write("<th>" + header + "</th>");
      });
      doc.write("</tr></thead>");
      
      // 表格内容
      doc.write("<tbody>");
      this.filteredAccounts.forEach(account => {
        const statusClass = "status-" + account.host.status;
        doc.write("<tr>");
        doc.write("<td>" + account.host.name + "</td>");
        doc.write("<td>" + account.host.ip + "</td>");
        doc.write("<td>" + account.username + (account.isDefault ? " (默认)" : "") + "</td>");
        doc.write("<td>" + (account.lastPasswordChange || "-") + "</td>");
        doc.write("<td>" + (account.passwordExpiryDate || "-") + "</td>");
        doc.write("<td class=\"" + statusClass + "\">" + account.host.status + "</td>");
        doc.write("<td>" + this.getPolicyName(account.policyId) + "</td>");
        doc.write("</tr>");
      });
      doc.write("</tbody>");
      doc.write("</table>");
      
      // 添加页脚
      doc.write("<div class=\"print-footer\">");
      doc.write("<p>注意：本文档包含敏感信息，请妥善保管</p>");
      doc.write("</div>");
      
      // 添加自动打印脚本
      doc.write("<" + "script" + ">");
      doc.write("window.onload = function() { window.print(); }");
      doc.write("</" + "script" + ">");
      
      doc.write("</body>");
      doc.write("</html>");
      doc.close();
    },

    // 检查密码是否符合策略要求
    isPasswordCompliant(account) {
      if (!account.policyId) return { compliant: true, text: '无策略' };
      
      const password = account.password;
      const policy = this.policies.find(p => p.id === account.policyId);
      
      if (!policy) return { compliant: true, text: '无策略' };
      
      const checks = [];
      
      // 检查长度
      if (password.length < policy.minLength) {
        checks.push(`密码长度不足 ${policy.minLength} 位`);
      }
      
      // 检查大写字母
      if (policy.requireUppercase && !/[A-Z]/.test(password)) {
        checks.push('缺少大写字母');
      }
      
      // 检查小写字母
      if (policy.requireLowercase && !/[a-z]/.test(password)) {
        checks.push('缺少小写字母');
      }
      
      // 检查数字
      if (policy.requireNumbers && !/[0-9]/.test(password)) {
        checks.push('缺少数字');
      }
      
      // 检查特殊字符
      if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {
        checks.push('缺少特殊字符');
      }
      
      // 检查是否包含用户名
      if (policy.forbidUsername && password.toLowerCase().includes(account.username.toLowerCase())) {
        checks.push('密码包含用户名');
      }
      
      return {
        compliant: checks.length === 0,
        text: checks.length === 0 ? '符合策略' : `不符合: ${checks.join(', ')}`,
        details: checks
      };
    }
  },
  created() {
    // 初始化日期和时间
    const today = new Date()
    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]
    this.batchUpdateModal.scheduledTime = '03:00'
  }
}
</script>